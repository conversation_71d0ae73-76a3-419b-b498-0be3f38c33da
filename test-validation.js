#!/usr/bin/env node

/**
 * JSP2React 验证功能测试脚本
 * 演示完整的验证流程，包括进程管理、智能内容对比和配置管理
 */

const { PuppeteerValidator } = require('./src/tools/PuppeteerValidator');
const { ProcessManager } = require('./src/tools/ProcessManager');
const { ContentComparator } = require('./src/tools/ContentComparator');
const { ValidationConfig } = require('./src/config/validation-config');
const chalk = require('chalk');
const fs = require('fs-extra');
const path = require('path');

class ValidationTest {
  constructor() {
    this.processManager = new ProcessManager({ verbose: true });
    this.runningProcesses = [];
  }

  async run() {
    console.log(chalk.blue('🧪 JSP2React 验证功能测试'));
    console.log(chalk.gray('=' .repeat(60)));

    try {
      // 1. 测试配置管理
      await this.testConfigManagement();

      // 2. 测试进程管理
      await this.testProcessManagement();

      // 3. 测试内容对比
      await this.testContentComparison();

      // 4. 测试完整验证流程
      await this.testFullValidation();

      console.log(chalk.green('\n🎉 所有测试完成！'));

    } catch (error) {
      console.error(chalk.red('\n❌ 测试失败:'), error.message);
      console.error(chalk.gray(error.stack));
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 测试配置管理
   */
  async testConfigManagement() {
    console.log(chalk.blue('\n📋 测试 1: 配置管理'));

    // 创建配置实例
    const config = new ValidationConfig();
    
    // 测试默认配置
    console.log(chalk.gray('测试默认配置...'));
    const jspUrl = config.get('servers.jsp.baseUrl');
    const reactUrl = config.get('servers.react.baseUrl');
    console.log(chalk.green(`✅ JSP URL: ${jspUrl}`));
    console.log(chalk.green(`✅ React URL: ${reactUrl}`));

    // 测试配置验证
    const validation = config.validate();
    if (validation.valid) {
      console.log(chalk.green('✅ 配置验证通过'));
    } else {
      console.log(chalk.yellow('⚠️  配置验证警告:'));
      validation.errors.forEach(error => {
        console.log(chalk.gray(`  - ${error}`));
      });
    }

    // 生成示例配置
    await config.generateExampleConfig('./validation-config.example.json');
    console.log(chalk.green('✅ 示例配置已生成'));
  }

  /**
   * 测试进程管理
   */
  async testProcessManagement() {
    console.log(chalk.blue('\n📋 测试 2: 进程管理'));

    // 检测项目类型
    console.log(chalk.gray('检测项目类型...'));
    
    const currentDir = process.cwd();
    const projectTypes = await this.processManager.detectProjectType(currentDir);
    
    if (projectTypes.length > 0) {
      console.log(chalk.green('✅ 检测到项目类型:'));
      projectTypes.forEach(type => {
        console.log(chalk.gray(`  - ${type.description} (${type.configFile})`));
      });
    } else {
      console.log(chalk.yellow('⚠️  未检测到支持的项目类型'));
    }

    // 测试健康检查
    console.log(chalk.gray('测试服务器健康检查...'));
    const health = await this.processManager.checkServerHealth('https://www.google.com');
    console.log(chalk.green(`✅ 健康检查结果: ${health.healthy ? '健康' : '不健康'}`));
  }

  /**
   * 测试内容对比
   */
  async testContentComparison() {
    console.log(chalk.blue('\n📋 测试 3: 内容对比'));

    const comparator = new ContentComparator({
      verbose: true,
      aiApiKey: process.env.OPENAI_API_KEY
    });

    // 创建模拟页面内容
    const jspContent = {
      title: 'JSP 测试页面',
      textContent: '这是一个 JSP 页面的主要内容，包含用户信息和产品列表。',
      structure: {
        headings: [
          { level: 1, text: '欢迎来到我们的网站', id: 'welcome' },
          { level: 2, text: '产品列表', id: 'products' }
        ],
        paragraphs: [
          { text: '我们提供最优质的产品和服务。', length: 15 },
          { text: '请浏览我们的产品目录。', length: 12 }
        ],
        forms: [
          { action: '/submit', method: 'post', inputCount: 3 }
        ]
      },
      semanticBlocks: [
        { tag: 'header', text: '网站头部内容', wordCount: 5 },
        { tag: 'main', text: '主要内容区域', wordCount: 5 }
      ]
    };

    const reactContent = {
      title: 'React 测试页面',
      textContent: '这是一个 React 页面的主要内容，包含用户信息和产品列表。',
      structure: {
        headings: [
          { level: 1, text: '欢迎来到我们的网站', id: 'welcome' },
          { level: 2, text: '产品列表', id: 'products' }
        ],
        paragraphs: [
          { text: '我们提供最优质的产品和服务。', length: 15 },
          { text: '请浏览我们的产品目录。', length: 12 }
        ],
        forms: [
          { action: '/submit', method: 'post', inputCount: 3 }
        ]
      },
      semanticBlocks: [
        { tag: 'header', text: '网站头部内容', wordCount: 5 },
        { tag: 'main', text: '主要内容区域', wordCount: 5 }
      ]
    };

    console.log(chalk.gray('进行内容对比...'));
    const comparison = await comparator.comparePageContents(jspContent, reactContent);
    
    console.log(chalk.green(`✅ 结构相似度: ${(comparison.structureScore * 100).toFixed(1)}%`));
    console.log(chalk.green(`✅ 内容相似度: ${(comparison.contentScore * 100).toFixed(1)}%`));
    console.log(chalk.green(`✅ 语义相似度: ${(comparison.semanticScore * 100).toFixed(1)}%`));
    console.log(chalk.green(`✅ 总体相似度: ${(comparison.overallScore * 100).toFixed(1)}%`));

    if (comparison.recommendations.length > 0) {
      console.log(chalk.blue('💡 改进建议:'));
      comparison.recommendations.forEach(rec => {
        console.log(chalk.gray(`  - ${rec}`));
      });
    }
  }

  /**
   * 测试完整验证流程
   */
  async testFullValidation() {
    console.log(chalk.blue('\n📋 测试 4: 完整验证流程'));

    // 创建模拟转换结果
    const mockResults = [
      {
        file: {
          name: 'test.jsp',
          relativePath: 'test.jsp',
          path: './test.jsp'
        },
        componentName: 'TestComponent',
        reactCode: 'export default function TestComponent() { return <div>Test</div>; }'
      }
    ];

    // 确保结果目录存在
    await fs.ensureDir('./test-results');
    await fs.writeJson('./test-results/conversion-results.json', mockResults, { spaces: 2 });

    // 创建验证器（使用模拟模式，不实际启动浏览器）
    console.log(chalk.gray('创建验证器...'));
    const validator = new PuppeteerValidator({
      jspBaseUrl: 'http://localhost:8080',
      reactBaseUrl: 'http://localhost:3000',
      headless: true,
      screenshotDir: './test-results',
      verbose: true,
      performanceMonitoring: true,
      visualComparison: false, // 关闭视觉对比以避免实际启动浏览器
      configPath: './validation-config.example.json'
    });

    console.log(chalk.green('✅ 验证器创建成功'));
    console.log(chalk.gray('配置信息:'));
    console.log(chalk.gray(`  JSP URL: ${validator.options.jspBaseUrl}`));
    console.log(chalk.gray(`  React URL: ${validator.options.reactBaseUrl}`));
    console.log(chalk.gray(`  截图目录: ${validator.options.screenshotDir}`));
    console.log(chalk.gray(`  性能监控: ${validator.options.performanceMonitoring ? '启用' : '禁用'}`));

    // 测试配置验证
    const configValidation = validator.validationConfig.validate();
    if (configValidation.valid) {
      console.log(chalk.green('✅ 验证器配置有效'));
    } else {
      console.log(chalk.yellow('⚠️  验证器配置警告:'));
      configValidation.errors.forEach(error => {
        console.log(chalk.gray(`  - ${error}`));
      });
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    console.log(chalk.blue('\n🧹 清理测试资源...'));
    
    try {
      // 停止所有进程
      await this.processManager.stopAllProcesses();
      
      // 清理测试文件
      const testFiles = [
        './validation-config.example.json',
        './test-results'
      ];
      
      for (const file of testFiles) {
        if (await fs.pathExists(file)) {
          await fs.remove(file);
          console.log(chalk.gray(`✓ 已删除: ${file}`));
        }
      }
      
      console.log(chalk.green('✅ 清理完成'));
    } catch (error) {
      console.log(chalk.yellow(`⚠️  清理过程中出现错误: ${error.message}`));
    }
  }
}

// 运行测试
if (require.main === module) {
  const test = new ValidationTest();
  test.run().catch(error => {
    console.error(chalk.red('测试运行失败:'), error);
    process.exit(1);
  });
}

module.exports = { ValidationTest };
