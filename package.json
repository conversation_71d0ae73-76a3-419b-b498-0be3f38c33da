{"name": "jsp2react-agent", "version": "1.0.0", "description": "JSP 转 React 的 AI Agent CLI 工具 - 渐进式迁移解决方案", "main": "src/cli.js", "bin": {"jsp2react": "./src/cli.js"}, "scripts": {"start": "node src/cli.js", "convert": "node src/cli.js convert", "analyze": "node src/cli.js analyze", "demo": "node src/cli.js demo", "setup": "node src/cli.js setup", "validate": "node src/cli.js validate", "fix": "node src/cli.js fix", "test": "node src/test.js", "dev": "cd fixtures/target && npm run dev", "build": "cd fixtures/target && npm run build", "web": "node src/web/server.js", "full-demo": "node demo.js", "validate-demo": "node validate-demo.js", "intelligent-validation": "node examples/intelligent-validation.js", "test-validation": "node test-validation.js", "puppeteer-validation": "node examples/puppeteer-validation.js", "complete-validation": "node examples/complete-validation-example.js"}, "keywords": ["jsp", "react", "migration", "ai", "agent", "conversion", "legacy", "modernization"], "author": "phodal", "license": "MIT", "dependencies": {"chalk": "^4.1.2", "commander": "^11.0.0", "dotenv": "^16.3.1", "express": "^4.18.2", "fs-extra": "^11.1.1", "pixelmatch": "^7.1.0", "pngjs": "^7.0.0", "puppeteer": "^21.0.0"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/aise-workshop/jsp2react-agent"}, "bugs": {"url": "https://github.com/aise-workshop/jsp2react-agent/issues"}, "homepage": "https://github.com/aise-workshop/jsp2react-agent#readme"}