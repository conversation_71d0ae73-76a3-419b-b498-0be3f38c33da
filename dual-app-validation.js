#!/usr/bin/env node

/**
 * 双应用验证脚本
 * 同时启动 JSP 和 React 应用进行真实对比
 */

const { ProcessManager } = require('./src/tools/ProcessManager');
const { PuppeteerValidator } = require('./src/tools/PuppeteerValidator');
const chalk = require('chalk');
const fs = require('fs-extra');
const path = require('path');

class DualAppValidation {
  constructor() {
    this.processManager = new ProcessManager({ verbose: true });
    this.runningProcesses = [];
    this.validator = null;
  }

  async run() {
    console.log(chalk.blue('🚀 JSP vs React 双应用验证'));
    console.log(chalk.gray('同时启动 JSP 和 React 应用进行真实对比'));
    console.log(chalk.gray('=' .repeat(70)));

    try {
      // 1. 检查项目环境
      await this.checkEnvironment();

      // 2. 启动 JSP 应用
      await this.startJSPApplication();

      // 3. 启动 React 应用
      await this.startReactApplication();

      // 4. 等待两个应用都就绪
      await this.waitForApplications();

      // 5. 进行真实的页面验证对比
      await this.performRealValidation();

      // 6. 生成对比报告
      await this.generateComparisonReport();

      console.log(chalk.green('\n🎉 双应用验证完成！'));

    } catch (error) {
      console.error(chalk.red('\n❌ 验证失败:'), error.message);
      console.error(chalk.gray(error.stack));
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 检查项目环境
   */
  async checkEnvironment() {
    console.log(chalk.blue('\n📋 步骤 1: 检查项目环境'));

    // 检查 JSP 项目
    const jspProjectPath = './fixtures/source';
    if (!await fs.pathExists(path.join(jspProjectPath, 'pom.xml'))) {
      throw new Error(`JSP 项目不存在: ${jspProjectPath}/pom.xml`);
    }

    const jspTypes = await this.processManager.detectProjectType(jspProjectPath);
    console.log(chalk.green('✅ JSP 项目检测结果:'));
    jspTypes.forEach(type => {
      console.log(chalk.gray(`  - ${type.description} (${type.configFile})`));
    });

    // 检查 React 项目
    const reactProjectPath = './fixtures/target';
    if (!await fs.pathExists(path.join(reactProjectPath, 'package.json'))) {
      throw new Error(`React 项目不存在: ${reactProjectPath}/package.json`);
    }

    const reactTypes = await this.processManager.detectProjectType(reactProjectPath);
    console.log(chalk.green('✅ React 项目检测结果:'));
    reactTypes.forEach(type => {
      console.log(chalk.gray(`  - ${type.description} (${type.configFile})`));
    });
  }

  /**
   * 启动 JSP 应用
   */
  async startJSPApplication() {
    console.log(chalk.blue('\n📋 步骤 2: 启动 JSP 应用'));

    try {
      console.log(chalk.gray('正在启动 JSP 应用 (Maven + Jetty)...'));
      
      const jspServer = await this.processManager.startServer('./fixtures/source', {
        port: 8080,
        preferredType: 'maven'
      });
      
      this.runningProcesses.push(jspServer);
      console.log(chalk.green(`✅ JSP 应用启动成功: ${jspServer.url}`));
      
    } catch (error) {
      console.log(chalk.yellow(`⚠️  JSP 应用启动失败: ${error.message}`));
      console.log(chalk.gray('尝试手动启动: cd fixtures/source && mvn jetty:run'));
      throw error;
    }
  }

  /**
   * 启动 React 应用
   */
  async startReactApplication() {
    console.log(chalk.blue('\n📋 步骤 3: 启动 React 应用'));

    try {
      console.log(chalk.gray('正在启动 React 应用...'));
      
      const reactServer = await this.processManager.startServer('./fixtures/target', {
        port: 3000,
        script: 'dev'
      });
      
      this.runningProcesses.push(reactServer);
      console.log(chalk.green(`✅ React 应用启动成功: ${reactServer.url}`));
      
    } catch (error) {
      console.log(chalk.yellow(`⚠️  React 应用启动失败: ${error.message}`));
      console.log(chalk.gray('尝试手动启动: cd fixtures/target && npm run dev'));
      throw error;
    }
  }

  /**
   * 等待应用就绪
   */
  async waitForApplications() {
    console.log(chalk.blue('\n📋 步骤 4: 等待应用就绪'));

    // 等待 JSP 应用
    console.log(chalk.gray('等待 JSP 应用就绪...'));
    const jspReady = await this.processManager.waitForServer('http://localhost:8080', 20, 3000);
    if (jspReady) {
      console.log(chalk.green('✅ JSP 应用已就绪'));
    } else {
      console.log(chalk.yellow('⚠️  JSP 应用可能未完全就绪，继续验证'));
    }

    // 等待 React 应用
    console.log(chalk.gray('等待 React 应用就绪...'));
    const reactReady = await this.processManager.waitForServer('http://localhost:3000', 15, 2000);
    if (reactReady) {
      console.log(chalk.green('✅ React 应用已就绪'));
    } else {
      console.log(chalk.yellow('⚠️  React 应用可能未完全就绪，继续验证'));
    }
  }

  /**
   * 进行真实的页面验证对比
   */
  async performRealValidation() {
    console.log(chalk.blue('\n📋 步骤 5: 进行真实页面验证对比'));

    // 创建验证器
    this.validator = new PuppeteerValidator({
      jspBaseUrl: 'http://localhost:8080',
      reactBaseUrl: 'http://localhost:3000',
      headless: false, // 显示浏览器以便观察
      screenshotDir: './dual-app-validation-results',
      verbose: true,
      performanceMonitoring: true,
      visualComparison: true,
      errorAnalysis: true
    });

    await this.validator.initialize();
    console.log(chalk.green('✅ 验证器已初始化'));

    // 创建转换结果数据（基于实际的 JSP 文件）
    const conversionResults = [
      {
        file: {
          name: 'posts.jsp',
          relativePath: 'posts.jsp',
          path: './fixtures/source/src/main/webapp/posts.jsp'
        },
        componentName: 'Posts',
        reactCode: 'Posts component code...'
      }
    ];

    console.log(chalk.gray('开始验证页面对比...'));
    
    try {
      // 验证 JSP 页面
      console.log(chalk.gray('验证 JSP 页面: /posts.jsp'));
      const jspResult = await this.validator.validateJSPPage('http://localhost:8080/posts.jsp', 'posts.jsp');
      console.log(chalk.green(`✅ JSP 页面验证完成 (${jspResult.accessible ? '可访问' : '不可访问'})`));

      // 验证 React 页面
      console.log(chalk.gray('验证 React 页面: /posts'));
      const reactResult = await this.validator.validateReactPage('http://localhost:3000/posts', 'Posts');
      console.log(chalk.green(`✅ React 页面验证完成 (${reactResult.accessible ? '可访问' : '不可访问'})`));

      // 进行页面对比
      if (jspResult.accessible && reactResult.accessible) {
        console.log(chalk.gray('进行页面内容对比...'));
        const comparison = await this.validator.comparePages(jspResult, reactResult);
        console.log(chalk.green(`✅ 页面对比完成 - 相似度: ${comparison.structureSimilarity.toFixed(1)}%`));
        
        this.validationResults = {
          jsp: jspResult,
          react: reactResult,
          comparison: comparison
        };
      } else {
        console.log(chalk.yellow('⚠️  无法进行对比 - 部分页面不可访问'));
        this.validationResults = {
          jsp: jspResult,
          react: reactResult,
          comparison: null
        };
      }

    } catch (error) {
      console.log(chalk.red(`❌ 页面验证失败: ${error.message}`));
      throw error;
    }
  }

  /**
   * 生成对比报告
   */
  async generateComparisonReport() {
    console.log(chalk.blue('\n📋 步骤 6: 生成对比报告'));

    const reportDir = './dual-app-validation-results';
    await fs.ensureDir(reportDir);

    // 生成 JSON 报告
    const jsonReport = {
      timestamp: new Date().toISOString(),
      applications: {
        jsp: {
          url: 'http://localhost:8080',
          accessible: this.validationResults.jsp.accessible,
          errors: this.validationResults.jsp.errors.length,
          warnings: this.validationResults.jsp.warnings.length,
          loadTime: this.validationResults.jsp.loadTime
        },
        react: {
          url: 'http://localhost:3000',
          accessible: this.validationResults.react.accessible,
          errors: this.validationResults.react.errors.length,
          warnings: this.validationResults.react.warnings.length,
          loadTime: this.validationResults.react.loadTime
        }
      },
      comparison: this.validationResults.comparison,
      screenshots: {
        jsp: this.validationResults.jsp.screenshot,
        react: this.validationResults.react.screenshot
      }
    };

    const jsonPath = path.join(reportDir, 'validation-report.json');
    await fs.writeJson(jsonPath, jsonReport, { spaces: 2 });
    console.log(chalk.green(`✅ JSON 报告已生成: ${jsonPath}`));

    // 生成 HTML 报告
    const htmlContent = this.generateHTMLReport(jsonReport);
    const htmlPath = path.join(reportDir, 'validation-report.html');
    await fs.writeFile(htmlPath, htmlContent);
    console.log(chalk.green(`✅ HTML 报告已生成: ${htmlPath}`));
    console.log(chalk.cyan(`🌐 在浏览器中查看: file://${path.resolve(htmlPath)}`));

    // 显示摘要
    this.displaySummary(jsonReport);
  }

  /**
   * 生成 HTML 报告
   */
  generateHTMLReport(report) {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSP vs React 验证报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 30px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .section { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .app-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .app-card { padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .app-card.accessible { border-color: #28a745; background: #f8fff9; }
        .app-card.inaccessible { border-color: #dc3545; background: #fff8f8; }
        .metric { display: flex; justify-content: space-between; margin: 10px 0; }
        .screenshot { max-width: 100%; border: 1px solid #ddd; border-radius: 5px; }
        .comparison-score { font-size: 2em; font-weight: bold; text-align: center; padding: 20px; border-radius: 8px; }
        .score-excellent { background: #d4edda; color: #155724; }
        .score-good { background: #fff3cd; color: #856404; }
        .score-fair { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 JSP vs React 验证报告</h1>
            <p>生成时间: ${new Date(report.timestamp).toLocaleString('zh-CN')}</p>
            <p>本报告展示了 JSP 和 React 应用的真实对比结果</p>
        </div>

        <div class="section">
            <h2>📊 应用状态对比</h2>
            <div class="app-grid">
                <div class="app-card ${report.applications.jsp.accessible ? 'accessible' : 'inaccessible'}">
                    <h3>JSP 应用 (${report.applications.jsp.url})</h3>
                    <div class="metric">
                        <span>可访问性:</span>
                        <span>${report.applications.jsp.accessible ? '✅ 正常' : '❌ 异常'}</span>
                    </div>
                    <div class="metric">
                        <span>错误数量:</span>
                        <span>${report.applications.jsp.errors}</span>
                    </div>
                    <div class="metric">
                        <span>警告数量:</span>
                        <span>${report.applications.jsp.warnings}</span>
                    </div>
                    <div class="metric">
                        <span>加载时间:</span>
                        <span>${report.applications.jsp.loadTime}ms</span>
                    </div>
                </div>
                
                <div class="app-card ${report.applications.react.accessible ? 'accessible' : 'inaccessible'}">
                    <h3>React 应用 (${report.applications.react.url})</h3>
                    <div class="metric">
                        <span>可访问性:</span>
                        <span>${report.applications.react.accessible ? '✅ 正常' : '❌ 异常'}</span>
                    </div>
                    <div class="metric">
                        <span>错误数量:</span>
                        <span>${report.applications.react.errors}</span>
                    </div>
                    <div class="metric">
                        <span>警告数量:</span>
                        <span>${report.applications.react.warnings}</span>
                    </div>
                    <div class="metric">
                        <span>加载时间:</span>
                        <span>${report.applications.react.loadTime}ms</span>
                    </div>
                </div>
            </div>
        </div>

        ${report.comparison ? `
        <div class="section">
            <h2>🔍 页面相似度分析</h2>
            <div class="comparison-score ${this.getScoreClass(report.comparison.structureSimilarity / 100)}">
                ${report.comparison.structureSimilarity.toFixed(1)}% 相似度
            </div>
            <p>基于页面结构、内容和功能的综合分析结果</p>
        </div>
        ` : ''}

        ${report.screenshots.jsp || report.screenshots.react ? `
        <div class="section">
            <h2>📸 页面截图对比</h2>
            <div class="app-grid">
                ${report.screenshots.jsp ? `
                <div>
                    <h3>JSP 页面截图</h3>
                    <img src="${path.basename(report.screenshots.jsp)}" alt="JSP 页面" class="screenshot">
                </div>
                ` : ''}
                ${report.screenshots.react ? `
                <div>
                    <h3>React 页面截图</h3>
                    <img src="${path.basename(report.screenshots.react)}" alt="React 页面" class="screenshot">
                </div>
                ` : ''}
            </div>
        </div>
        ` : ''}

        <div class="section">
            <h2>🔧 技术说明</h2>
            <p>本验证报告通过以下方式生成:</p>
            <ul>
                <li><strong>真实应用对比</strong>: 同时启动 JSP 和 React 应用进行实时对比</li>
                <li><strong>自动化验证</strong>: 使用 Puppeteer 自动访问和分析页面</li>
                <li><strong>多维度分析</strong>: 从可访问性、性能、错误等多个维度进行分析</li>
                <li><strong>可视化对比</strong>: 生成页面截图进行视觉对比</li>
            </ul>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * 获取分数样式类
   */
  getScoreClass(score) {
    if (score >= 0.8) return 'score-excellent';
    if (score >= 0.6) return 'score-good';
    return 'score-fair';
  }

  /**
   * 显示摘要
   */
  displaySummary(report) {
    console.log(chalk.blue('\n📋 验证摘要:'));
    console.log(chalk.gray(`JSP 应用: ${report.applications.jsp.accessible ? '✅ 正常' : '❌ 异常'} (${report.applications.jsp.loadTime}ms)`));
    console.log(chalk.gray(`React 应用: ${report.applications.react.accessible ? '✅ 正常' : '❌ 异常'} (${report.applications.react.loadTime}ms)`));
    
    if (report.comparison) {
      const similarity = report.comparison.structureSimilarity;
      const color = similarity >= 80 ? chalk.green : similarity >= 60 ? chalk.yellow : chalk.red;
      console.log(color(`页面相似度: ${similarity.toFixed(1)}%`));
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    console.log(chalk.blue('\n🧹 清理资源...'));
    
    try {
      if (this.validator) {
        await this.validator.close();
        console.log(chalk.gray('✓ 验证器已关闭'));
      }
      
      await this.processManager.stopAllProcesses();
      console.log(chalk.gray('✓ 所有应用进程已停止'));
      
      console.log(chalk.green('✅ 资源清理完成'));
    } catch (error) {
      console.log(chalk.yellow(`⚠️  清理过程中出现错误: ${error.message}`));
    }
  }
}

// 运行验证
if (require.main === module) {
  const validation = new DualAppValidation();
  validation.run().catch(error => {
    console.error(chalk.red('验证运行失败:'), error);
    process.exit(1);
  });
}

module.exports = { DualAppValidation };
