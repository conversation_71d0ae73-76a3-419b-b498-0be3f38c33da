# JSP2React AI 自动化工具

一个智能的 JSP 转 React 自动化工具，集成了 AI 驱动的代码转换和智能验证功能。

## ✨ 核心功能

### 🤖 AI 驱动的代码转换
- **智能 JSP 解析**：自动分析 JSP 文件结构和依赖
- **React 组件生成**：生成现代化的 React 组件代码
- **嵌入式兼容**：支持渐进式迁移，生成可嵌入的 React 组件

### 🧠 智能验证系统
- **DOM 结构对比**：基于语义的页面结构分析，而非简单的像素对比
- **AI 语义分析**：使用 AI 进行深度内容相似度分析
- **性能监控**：全面的页面加载和渲染性能分析
- **错误检测**：自动捕获和分类 JavaScript 错误

### 🔧 自动化进程管理
- **多构建工具支持**：Maven、Gradle、npm、yarn 等
- **智能项目检测**：自动识别项目类型和配置
- **服务器管理**：自动启动、监控和关闭开发服务器
- **健康检查**：实时监控服务器状态

### ⚙️ 灵活配置管理
- **配置文件支持**：JSON 格式的灵活配置
- **阈值自定义**：可调整验证相似度阈值
- **权重配置**：自定义各项指标的权重
- **验证规则**：可配置的验证规则和忽略项

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 配置 AI API 密钥（可选，用于语义分析）
echo "OPENAI_API_KEY=your-api-key" >> .env
```

### 3. 运行演示

```bash
# 运行智能验证演示（推荐）
npm run intelligent-validation

# 运行验证功能测试
npm run test-validation

# 运行完整转换演示
npm run full-demo

# 启动 Web 界面
npm run web
```

## 📊 验证功能详解

### 智能内容对比
- **结构分析**：标题、段落、表单、列表等元素的对比
- **文本内容**：页面实际文本内容的相似度分析
- **语义理解**：使用 AI 进行深度语义相似度分析
- **多维评分**：结构、内容、语义、性能的综合评分

### 自动化验证流程
1. **项目检测**：自动识别 JSP 和 React 项目类型
2. **服务器启动**：自动启动开发服务器
3. **页面分析**：提取页面结构和内容
4. **智能对比**：多维度相似度分析
5. **报告生成**：详细的 HTML 验证报告

## 📁 项目结构

```
jsp2react-agent/
├── src/
│   ├── tools/
│   │   ├── PuppeteerValidator.js    # 智能验证器
│   │   ├── ContentComparator.js     # 内容对比器
│   │   └── ProcessManager.js        # 进程管理器
│   ├── config/
│   │   └── validation-config.js     # 配置管理
│   └── converters/
│       └── JSPToReactConverter.js   # JSP 转换器
├── examples/
│   ├── intelligent-validation.js   # 智能验证示例
│   └── puppeteer-validation.js     # 传统验证示例
├── docs/
│   └── VALIDATION_GUIDE.md         # 详细使用指南
└── test-validation.js              # 功能测试脚本
```

## 🔧 配置示例

创建 `validation-config.json`：

```json
{
  "general": {
    "verbose": true,
    "screenshotDir": "./validation-results"
  },
  "servers": {
    "jsp": { "baseUrl": "http://localhost:8080" },
    "react": { "baseUrl": "http://localhost:3000" }
  },
  "thresholds": {
    "structure": { "excellent": 0.9, "good": 0.7 },
    "content": { "excellent": 0.9, "good": 0.8 },
    "semantic": { "excellent": 0.9, "good": 0.8 }
  },
  "ai": {
    "enabled": true,
    "apiKey": "your-api-key",
    "model": "gpt-3.5-turbo"
  }
}
```

## 📈 验证报告

验证完成后生成交互式 HTML 报告，包含：

- **总体统计**：成功率、平均相似度、错误统计
- **详细对比**：每个页面的多维度分析
- **性能指标**：加载时间、渲染性能
- **改进建议**：基于 AI 分析的具体建议
- **可视化图表**：直观的数据展示

## 🎯 使用场景

- **渐进式迁移**：从 JSP 逐步迁移到 React
- **质量保证**：确保转换后的页面功能完整
- **性能优化**：对比转换前后的性能差异
- **自动化测试**：集成到 CI/CD 流水线

## 📚 文档

- [详细验证指南](docs/VALIDATION_GUIDE.md)
- [API 文档](docs/API.md)
- [配置参考](docs/CONFIG.md)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License