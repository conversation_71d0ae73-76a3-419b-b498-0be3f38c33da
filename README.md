# **JSP 转 React 的 AI Agent CLI 工具 - 增强版项目计划**


第一轮提示词：

```
我在设计一个渐进式的 JSP 转 React 的工具，第一步想借助 AI 把 JSP 翻译为 React Embedded 的方式来使用，帮我好好设计这个 AI Agent（参考一下你 Augment 的经验），需要确保页面可以直接跑起来？？？或者方便的方式直接打开进行测试。

如下是我之前写的 ai 服务调用 代码，有点太复杂了，你可以基于这个去转换：

const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
require('dotenv').config();
const ai = require('ai');
const aiSdkOpenai = require('@ai-sdk/openai');

const { createUnifiedLogService } = require('../infrastructure/logging');
```

```
你应该结合 Puppetter 来看看是否能成功转换 JSP 到 react 页面，看看有没有报错
```


```
现在你应该使用我的测试工程 @/Users/<USER>/ai/legacy/jsp2react-agent/fixtures/source/ 来进行转换，并确保流程是 OK 的，页面可以访问，也不会报错
```


```
我这是一个 CLI 工具，你不应该手动修复 。应该先编译之类的，然后报错，再设计  AI Agent 自动修复 编译错误；如果浏览器打开出错，应该使用 Puppetterer 或者 Playwright 去读取浏览器 console 的错误
```


```
我正在实现一个 JSP 转 React 的 AI 自动化工具。现在我需要实现：


工具集成了 Puppeteer 自动化验证功能，确保转换质量：

### 验证内容

1. **页面可访问性**: 检查 JSP 和 React 页面是否正常加载
2. **错误监控**: 捕获 JavaScript 错误和控制台警告
3. **结构对比**: 比较原始 JSP 和转换后 React 页面的结构相似度
4. **截图对比**: 生成页面截图用于视觉验证
5. **性能监控**: 记录页面加载时间和资源使用情况

你应该做一些搜索，看没有更好的截图方案，然后再实现。另外，请充分尝试这个截图功能，由于截图需要先启动老的应用，你还需要辅助做一个进程管理的功能，最好是能支持 Maven， Gradle 等不同工具，方便未来自动化
```


## 前端页面结果对比


- 像素级对比 -> DOM 元素或者结构对比？
- 页面内容或者结果对比