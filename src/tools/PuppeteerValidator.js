const puppeteer = require('puppeteer');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const pixelmatch = require('pixelmatch');
const { PNG } = require('pngjs');

/**
 * Puppeteer 验证工具
 * 使用 Puppeteer 验证 JSP 转 React 的转换结果
 * 包含页面可访问性、错误监控、结构对比、截图对比和性能监控功能
 */
class PuppeteerValidator {
  constructor(options = {}) {
    this.options = {
      jspBaseUrl: options.jspBaseUrl || 'http://localhost:8080',
      reactBaseUrl: options.reactBaseUrl || 'http://localhost:3000',
      headless: options.headless !== false,
      timeout: options.timeout || 30000,
      viewport: options.viewport || { width: 1280, height: 720 },
      screenshotDir: options.screenshotDir || './screenshots',
      verbose: options.verbose || false,
      // 新增配置选项
      performanceMonitoring: options.performanceMonitoring !== false,
      visualComparison: options.visualComparison !== false,
      errorAnalysis: options.errorAnalysis !== false,
      structureAnalysis: options.structureAnalysis !== false,
      // 性能阈值配置
      performanceThresholds: {
        loadTime: options.performanceThresholds?.loadTime || 5000, // 5秒
        firstContentfulPaint: options.performanceThresholds?.firstContentfulPaint || 2000, // 2秒
        largestContentfulPaint: options.performanceThresholds?.largestContentfulPaint || 4000, // 4秒
        cumulativeLayoutShift: options.performanceThresholds?.cumulativeLayoutShift || 0.1,
        firstInputDelay: options.performanceThresholds?.firstInputDelay || 100, // 100ms
        ...options.performanceThresholds
      },
      // 视觉对比阈值
      visualThresholds: {
        pixelDifferenceThreshold: options.visualThresholds?.pixelDifferenceThreshold || 0.1, // 10%
        structureSimilarityThreshold: options.visualThresholds?.structureSimilarityThreshold || 0.8, // 80%
        ...options.visualThresholds
      },
      ...options
    };

    this.browser = null;
    this.validationResults = [];
    this.performanceMetrics = [];
  }

  /**
   * 初始化 Puppeteer
   */
  async initialize() {
    console.log(chalk.blue('🚀 启动 Puppeteer 浏览器...'));
    
    this.browser = await puppeteer.launch({
      headless: this.options.headless,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    });

    await fs.ensureDir(this.options.screenshotDir);
    console.log(chalk.green('✅ Puppeteer 浏览器已启动'));
  }

  /**
   * 验证转换结果
   */
  async validateConversion(conversionResults) {
    if (!this.browser) {
      await this.initialize();
    }

    console.log(chalk.blue('🔍 开始验证转换结果...'));

    for (const result of conversionResults) {
      if (result.reactCode && result.file) {
        await this.validateSingleConversion(result);
      }
    }

    await this.generateValidationReport();
    console.log(chalk.green('✅ 验证完成'));

    return this.validationResults;
  }

  /**
   * 验证单个转换结果
   */
  async validateSingleConversion(conversionResult) {
    const { file, componentName } = conversionResult;
    const jspPath = this.getJSPPath(file.relativePath);
    
    console.log(chalk.gray(`🔍 验证 ${file.name} -> ${componentName}...`));

    const validation = {
      fileName: file.name,
      componentName,
      jspUrl: `${this.options.jspBaseUrl}/${jspPath}`,
      reactUrl: `${this.options.reactBaseUrl}/${this.getReactPath(componentName)}`,
      timestamp: new Date().toISOString(),
      results: {}
    };

    try {
      // 1. 验证原始 JSP 页面
      validation.results.jsp = await this.validateJSPPage(validation.jspUrl, file.name);

      // 2. 验证转换后的 React 页面
      validation.results.react = await this.validateReactPage(validation.reactUrl, componentName);

      // 3. 比较页面结构
      validation.results.comparison = await this.comparePages(validation.results.jsp, validation.results.react);

      // 4. 视觉对比
      if (this.options.visualComparison && validation.results.jsp.screenshot && validation.results.react.screenshot) {
        validation.results.visualComparison = await this.compareScreenshots(
          validation.results.jsp.screenshot,
          validation.results.react.screenshot,
          `${file.name}-${componentName}`
        );
      }

      // 5. 验证嵌入式版本
      validation.results.embedded = await this.validateEmbeddedVersion(conversionResult);

      validation.success = true;

    } catch (error) {
      validation.error = error.message;
      validation.success = false;
      console.error(chalk.red(`❌ 验证失败 ${file.name}: ${error.message}`));
    }

    this.validationResults.push(validation);
  }

  /**
   * 验证 JSP 页面
   */
  async validateJSPPage(url, fileName) {
    const page = await this.browser.newPage();
    await page.setViewport(this.options.viewport);

    const result = {
      url,
      accessible: false,
      errors: [],
      warnings: [],
      screenshot: null,
      content: null,
      elements: {},
      performance: {},
      loadTime: 0
    };

    try {
      // 启用性能监控
      if (this.options.performanceMonitoring) {
        await page.setCacheEnabled(false);
        await page.setRequestInterception(true);

        // 监听网络请求
        page.on('request', request => {
          request.continue();
        });
      }

      // 监听控制台错误和警告
      const consoleMessages = [];
      page.on('console', msg => {
        const message = {
          type: msg.type(),
          text: msg.text(),
          location: msg.location(),
          timestamp: new Date().toISOString()
        };
        consoleMessages.push(message);

        if (msg.type() === 'error') {
          result.errors.push(this.categorizeError(msg.text()));
        } else if (msg.type() === 'warning') {
          result.warnings.push(msg.text());
        }
      });

      // 监听页面错误
      page.on('pageerror', error => {
        result.errors.push({
          type: 'page_error',
          message: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString()
        });
      });

      // 监听请求失败
      const failedRequests = [];
      page.on('requestfailed', request => {
        const failedRequest = {
          url: request.url(),
          method: request.method(),
          errorText: request.failure().errorText,
          timestamp: new Date().toISOString()
        };
        failedRequests.push(failedRequest);
        result.errors.push(`Request Failed: ${request.url()} - ${request.failure().errorText}`);
      });

      console.log(chalk.gray(`  📄 访问 JSP 页面: ${url}`));

      // 记录开始时间
      const startTime = Date.now();

      const response = await page.goto(url, {
        waitUntil: 'networkidle0',
        timeout: this.options.timeout
      });

      // 记录加载时间
      result.loadTime = Date.now() - startTime;

      if (response && response.ok()) {
        result.accessible = true;
        result.statusCode = response.status();

        // 等待页面完全加载
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 收集性能指标
        if (this.options.performanceMonitoring) {
          result.performance = await this.collectPerformanceMetrics(page);
        }

        // 获取页面内容
        result.content = await page.content();

        // 分析页面元素
        result.elements = await this.analyzePageElements(page);

        // 截图
        const screenshotPath = path.join(
          this.options.screenshotDir,
          `jsp-${fileName.replace('.jsp', '')}-${Date.now()}.png`
        );
        await page.screenshot({ path: screenshotPath, fullPage: true });
        result.screenshot = screenshotPath;

        // 保存控制台消息和失败请求
        result.consoleMessages = consoleMessages;
        result.failedRequests = failedRequests;

        console.log(chalk.green(`  ✅ JSP 页面访问成功 (${result.loadTime}ms)`));

      } else {
        result.errors.push({
          type: 'http_error',
          message: `HTTP ${response.status()}: ${response.statusText()}`,
          statusCode: response.status(),
          timestamp: new Date().toISOString()
        });
      }

    } catch (error) {
      result.errors.push(`Navigation Error: ${error.message}`);
      console.log(chalk.yellow(`  ⚠️  JSP 页面访问失败: ${error.message}`));
    } finally {
      await page.close();
    }

    return result;
  }

  /**
   * 验证 React 页面
   */
  async validateReactPage(url, componentName) {
    const page = await this.browser.newPage();
    await page.setViewport(this.options.viewport);

    const result = {
      url,
      accessible: false,
      errors: [],
      warnings: [],
      screenshot: null,
      content: null,
      elements: {},
      reactErrors: [],
      performance: {},
      loadTime: 0
    };

    try {
      // 启用性能监控
      if (this.options.performanceMonitoring) {
        await page.setCacheEnabled(false);
        await page.setRequestInterception(true);

        page.on('request', request => {
          request.continue();
        });
      }

      // 监听 React 错误
      await page.evaluateOnNewDocument(() => {
        window.reactErrors = [];
        window.consoleMessages = [];

        const originalError = console.error;
        const originalWarn = console.warn;

        console.error = (...args) => {
          const message = args.join(' ');
          window.consoleMessages.push({ type: 'error', message, timestamp: Date.now() });
          if (message.includes('React') || message.includes('Warning:') || message.includes('Component')) {
            window.reactErrors.push(message);
          }
          originalError.apply(console, args);
        };

        console.warn = (...args) => {
          const message = args.join(' ');
          window.consoleMessages.push({ type: 'warning', message, timestamp: Date.now() });
          originalWarn.apply(console, args);
        };
      });

      // 监听控制台消息
      const consoleMessages = [];
      page.on('console', msg => {
        const message = {
          type: msg.type(),
          text: msg.text(),
          location: msg.location(),
          timestamp: new Date().toISOString()
        };
        consoleMessages.push(message);

        if (msg.type() === 'error') {
          result.errors.push(this.categorizeError(msg.text()));
        } else if (msg.type() === 'warning') {
          result.warnings.push(msg.text());
        }
      });

      // 监听页面错误
      page.on('pageerror', error => {
        result.errors.push({
          type: 'page_error',
          message: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString()
        });
      });

      // 监听请求失败
      const failedRequests = [];
      page.on('requestfailed', request => {
        const failedRequest = {
          url: request.url(),
          method: request.method(),
          errorText: request.failure().errorText,
          timestamp: new Date().toISOString()
        };
        failedRequests.push(failedRequest);
        result.errors.push(`Request Failed: ${request.url()} - ${request.failure().errorText}`);
      });

      console.log(chalk.gray(`  ⚛️  访问 React 页面: ${url}`));

      // 记录开始时间
      const startTime = Date.now();

      const response = await page.goto(url, {
        waitUntil: 'networkidle0',
        timeout: this.options.timeout
      });

      // 记录加载时间
      result.loadTime = Date.now() - startTime;

      if (response && response.ok()) {
        result.accessible = true;
        result.statusCode = response.status();

        // 等待 React 组件渲染
        await new Promise(resolve => setTimeout(resolve, 3000));

        // 检查 React 是否正确加载
        const reactInfo = await page.evaluate(() => {
          const info = {
            reactLoaded: typeof window.React !== 'undefined',
            reactDOMLoaded: typeof window.ReactDOM !== 'undefined',
            reactVersion: window.React ? window.React.version : null,
            reactDevTools: typeof window.__REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined'
          };

          // 检查是否有 React 组件挂载
          const reactRoots = document.querySelectorAll('[data-reactroot], #root, #app');
          info.hasReactRoots = reactRoots.length > 0;

          return info;
        });

        if (!reactInfo.reactLoaded) {
          result.warnings.push('React 库未正确加载');
        }

        if (!reactInfo.hasReactRoots) {
          result.warnings.push('未找到 React 根元素');
        }

        // 收集性能指标
        if (this.options.performanceMonitoring) {
          result.performance = await this.collectPerformanceMetrics(page);
        }

        // 获取 React 错误和控制台消息
        const pageData = await page.evaluate(() => ({
          reactErrors: window.reactErrors || [],
          consoleMessages: window.consoleMessages || []
        }));

        result.reactErrors = pageData.reactErrors;
        result.reactInfo = reactInfo;

        // 获取页面内容
        result.content = await page.content();

        // 分析页面元素
        result.elements = await this.analyzePageElements(page);

        // 截图
        const screenshotPath = path.join(
          this.options.screenshotDir,
          `react-${componentName}-${Date.now()}.png`
        );
        await page.screenshot({ path: screenshotPath, fullPage: true });
        result.screenshot = screenshotPath;

        // 保存控制台消息和失败请求
        result.consoleMessages = consoleMessages;
        result.failedRequests = failedRequests;

        console.log(chalk.green(`  ✅ React 页面访问成功 (${result.loadTime}ms)`));

      } else {
        result.errors.push({
          type: 'http_error',
          message: `HTTP ${response.status()}: ${response.statusText()}`,
          statusCode: response.status(),
          timestamp: new Date().toISOString()
        });
      }

    } catch (error) {
      result.errors.push(`Navigation Error: ${error.message}`);
      console.log(chalk.yellow(`  ⚠️  React 页面访问失败: ${error.message}`));
    } finally {
      await page.close();
    }

    return result;
  }

  /**
   * 收集性能指标
   */
  async collectPerformanceMetrics(page) {
    const metrics = await page.evaluate(() => {
      const performance = window.performance;
      const navigation = performance.getEntriesByType('navigation')[0];
      const paint = performance.getEntriesByType('paint');

      const result = {
        // 基本时间指标
        domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
        loadComplete: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,

        // 网络时间
        dnsLookup: navigation ? navigation.domainLookupEnd - navigation.domainLookupStart : 0,
        tcpConnect: navigation ? navigation.connectEnd - navigation.connectStart : 0,
        serverResponse: navigation ? navigation.responseEnd - navigation.requestStart : 0,

        // 渲染时间
        firstPaint: 0,
        firstContentfulPaint: 0,

        // 资源统计
        resourceCount: performance.getEntriesByType('resource').length,

        // 内存使用（如果可用）
        memoryUsage: window.performance.memory ? {
          usedJSHeapSize: window.performance.memory.usedJSHeapSize,
          totalJSHeapSize: window.performance.memory.totalJSHeapSize,
          jsHeapSizeLimit: window.performance.memory.jsHeapSizeLimit
        } : null
      };

      // 提取绘制时间
      paint.forEach(entry => {
        if (entry.name === 'first-paint') {
          result.firstPaint = entry.startTime;
        } else if (entry.name === 'first-contentful-paint') {
          result.firstContentfulPaint = entry.startTime;
        }
      });

      return result;
    });

    // 添加 Core Web Vitals（如果可用）
    try {
      const vitals = await page.evaluate(() => {
        return new Promise((resolve) => {
          if (typeof window.webVitals !== 'undefined') {
            const vitals = {};

            window.webVitals.getCLS(metric => vitals.cls = metric.value);
            window.webVitals.getFID(metric => vitals.fid = metric.value);
            window.webVitals.getLCP(metric => vitals.lcp = metric.value);

            setTimeout(() => resolve(vitals), 1000);
          } else {
            resolve({});
          }
        });
      });

      metrics.coreWebVitals = vitals;
    } catch (error) {
      metrics.coreWebVitals = { error: 'Core Web Vitals not available' };
    }

    return metrics;
  }

  /**
   * 错误分类
   */
  categorizeError(errorText) {
    const categories = {
      javascript: /javascript|js|script/i,
      network: /network|fetch|xhr|ajax|cors/i,
      react: /react|jsx|component|hook/i,
      css: /css|style|stylesheet/i,
      security: /security|csp|cors|mixed content/i,
      performance: /performance|timeout|slow/i
    };

    let category = 'unknown';
    for (const [cat, pattern] of Object.entries(categories)) {
      if (pattern.test(errorText)) {
        category = cat;
        break;
      }
    }

    return {
      type: 'console_error',
      category,
      message: errorText,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 分析页面元素
   */
  async analyzePageElements(page) {
    return await page.evaluate(() => {
      const elements = {
        title: document.title,
        headings: Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6')).map(h => ({
          tag: h.tagName.toLowerCase(),
          text: h.textContent.trim()
        })),
        forms: Array.from(document.querySelectorAll('form')).map(form => ({
          action: form.action,
          method: form.method,
          inputs: Array.from(form.querySelectorAll('input')).length
        })),
        links: Array.from(document.querySelectorAll('a[href]')).map(a => ({
          href: a.href,
          text: a.textContent.trim()
        })),
        images: Array.from(document.querySelectorAll('img')).map(img => ({
          src: img.src,
          alt: img.alt
        })),
        scripts: Array.from(document.querySelectorAll('script[src]')).map(script => script.src),
        styles: Array.from(document.querySelectorAll('link[rel="stylesheet"]')).map(link => link.href),

        // 新增：更详细的元素分析
        buttons: Array.from(document.querySelectorAll('button, input[type="button"], input[type="submit"]')).length,
        tables: Array.from(document.querySelectorAll('table')).length,
        lists: Array.from(document.querySelectorAll('ul, ol')).length,
        divs: Array.from(document.querySelectorAll('div')).length,

        // 文本内容统计
        textContent: document.body ? document.body.textContent.trim().length : 0,

        // 可访问性相关
        altMissingImages: Array.from(document.querySelectorAll('img:not([alt])')).length,
        emptyLinks: Array.from(document.querySelectorAll('a[href]:empty')).length
      };

      return elements;
    });
  }

  /**
   * 比较页面结构
   */
  async comparePages(jspResult, reactResult) {
    const comparison = {
      structureSimilarity: 0,
      differences: [],
      similarities: [],
      detailedComparison: {},
      score: {
        content: 0,
        structure: 0,
        functionality: 0,
        overall: 0
      }
    };

    if (!jspResult.accessible || !reactResult.accessible) {
      comparison.differences.push('其中一个页面无法访问');
      return comparison;
    }

    const jsp = jspResult.elements;
    const react = reactResult.elements;

    // 内容比较
    const contentComparison = this.compareContent(jsp, react);
    comparison.detailedComparison.content = contentComparison;
    comparison.score.content = contentComparison.score;

    // 结构比较
    const structureComparison = this.compareStructure(jsp, react);
    comparison.detailedComparison.structure = structureComparison;
    comparison.score.structure = structureComparison.score;

    // 功能比较
    const functionalityComparison = this.compareFunctionality(jsp, react);
    comparison.detailedComparison.functionality = functionalityComparison;
    comparison.score.functionality = functionalityComparison.score;

    // 性能比较
    if (jspResult.performance && reactResult.performance) {
      const performanceComparison = this.comparePerformance(jspResult.performance, reactResult.performance);
      comparison.detailedComparison.performance = performanceComparison;
    }

    // 计算总体相似度
    comparison.score.overall = (
      comparison.score.content * 0.4 +
      comparison.score.structure * 0.3 +
      comparison.score.functionality * 0.3
    );

    comparison.structureSimilarity = comparison.score.overall;

    // 生成摘要
    this.generateComparisonSummary(comparison);

    return comparison;
  }

  /**
   * 比较内容
   */
  compareContent(jsp, react) {
    const comparison = {
      score: 0,
      details: {},
      similarities: [],
      differences: []
    };

    let matches = 0;
    let total = 0;

    // 比较标题
    total++;
    if (jsp.title === react.title) {
      matches++;
      comparison.similarities.push('页面标题相同');
    } else {
      comparison.differences.push(`标题不同: JSP="${jsp.title}" vs React="${react.title}"`);
    }

    // 比较标题数量和内容
    total++;
    const jspHeadingTexts = jsp.headings.map(h => h.text).sort();
    const reactHeadingTexts = react.headings.map(h => h.text).sort();
    const headingSimilarity = this.calculateTextSimilarity(jspHeadingTexts, reactHeadingTexts);

    if (headingSimilarity > 0.8) {
      matches++;
      comparison.similarities.push(`标题内容相似度: ${(headingSimilarity * 100).toFixed(1)}%`);
    } else {
      comparison.differences.push(`标题内容差异较大: ${(headingSimilarity * 100).toFixed(1)}%`);
    }

    // 比较文本内容长度
    total++;
    const textLengthRatio = Math.min(jsp.textContent, react.textContent) / Math.max(jsp.textContent, react.textContent);
    if (textLengthRatio > 0.8) {
      matches++;
      comparison.similarities.push(`文本内容长度相近: ${textLengthRatio.toFixed(2)}`);
    } else {
      comparison.differences.push(`文本内容长度差异: JSP=${jsp.textContent} vs React=${react.textContent}`);
    }

    comparison.score = matches / total;
    comparison.details = {
      titleMatch: jsp.title === react.title,
      headingSimilarity,
      textLengthRatio,
      jspHeadings: jsp.headings.length,
      reactHeadings: react.headings.length
    };

    return comparison;
  }

  /**
   * 比较结构
   */
  compareStructure(jsp, react) {
    const comparison = {
      score: 0,
      details: {},
      similarities: [],
      differences: []
    };

    let matches = 0;
    let total = 0;

    // 比较各种元素数量
    const elements = ['forms', 'links', 'images', 'buttons', 'tables', 'lists'];

    elements.forEach(element => {
      total++;
      const jspCount = jsp[element]?.length || jsp[element] || 0;
      const reactCount = react[element]?.length || react[element] || 0;

      const ratio = jspCount === 0 && reactCount === 0 ? 1 :
                   Math.min(jspCount, reactCount) / Math.max(jspCount, reactCount);

      if (ratio > 0.8) {
        matches++;
        comparison.similarities.push(`${element}数量相近: JSP=${jspCount}, React=${reactCount}`);
      } else {
        comparison.differences.push(`${element}数量差异: JSP=${jspCount}, React=${reactCount}`);
      }
    });

    comparison.score = matches / total;
    comparison.details = {
      elementCounts: {
        jsp: elements.reduce((acc, el) => ({ ...acc, [el]: jsp[el]?.length || jsp[el] || 0 }), {}),
        react: elements.reduce((acc, el) => ({ ...acc, [el]: react[el]?.length || react[el] || 0 }), {})
      }
    };

    return comparison;
  }

  /**
   * 比较功能
   */
  compareFunctionality(jsp, react) {
    const comparison = {
      score: 0,
      details: {},
      similarities: [],
      differences: []
    };

    let matches = 0;
    let total = 0;

    // 比较表单功能
    total++;
    const jspForms = jsp.forms || [];
    const reactForms = react.forms || [];

    if (jspForms.length === reactForms.length) {
      matches++;
      comparison.similarities.push(`表单数量相同: ${jspForms.length}`);
    } else {
      comparison.differences.push(`表单数量不同: JSP=${jspForms.length} vs React=${reactForms.length}`);
    }

    // 比较链接功能
    total++;
    const jspLinks = jsp.links || [];
    const reactLinks = react.links || [];
    const linkSimilarity = this.calculateLinkSimilarity(jspLinks, reactLinks);

    if (linkSimilarity > 0.7) {
      matches++;
      comparison.similarities.push(`链接相似度: ${(linkSimilarity * 100).toFixed(1)}%`);
    } else {
      comparison.differences.push(`链接差异较大: ${(linkSimilarity * 100).toFixed(1)}%`);
    }

    // 比较可访问性
    total++;
    const jspAccessibility = this.calculateAccessibilityScore(jsp);
    const reactAccessibility = this.calculateAccessibilityScore(react);

    if (Math.abs(jspAccessibility - reactAccessibility) < 0.2) {
      matches++;
      comparison.similarities.push('可访问性水平相近');
    } else {
      comparison.differences.push(`可访问性差异: JSP=${jspAccessibility.toFixed(2)} vs React=${reactAccessibility.toFixed(2)}`);
    }

    comparison.score = matches / total;
    comparison.details = {
      formComparison: { jsp: jspForms.length, react: reactForms.length },
      linkSimilarity,
      accessibility: { jsp: jspAccessibility, react: reactAccessibility }
    };

    return comparison;
  }

  /**
   * 比较性能指标
   */
  comparePerformance(jspPerf, reactPerf) {
    const comparison = {
      loadTime: {
        jsp: jspPerf.loadComplete || 0,
        react: reactPerf.loadComplete || 0,
        difference: Math.abs((jspPerf.loadComplete || 0) - (reactPerf.loadComplete || 0)),
        winner: (jspPerf.loadComplete || 0) < (reactPerf.loadComplete || 0) ? 'JSP' : 'React'
      },
      firstContentfulPaint: {
        jsp: jspPerf.firstContentfulPaint || 0,
        react: reactPerf.firstContentfulPaint || 0,
        difference: Math.abs((jspPerf.firstContentfulPaint || 0) - (reactPerf.firstContentfulPaint || 0)),
        winner: (jspPerf.firstContentfulPaint || 0) < (reactPerf.firstContentfulPaint || 0) ? 'JSP' : 'React'
      },
      resourceCount: {
        jsp: jspPerf.resourceCount || 0,
        react: reactPerf.resourceCount || 0,
        difference: Math.abs((jspPerf.resourceCount || 0) - (reactPerf.resourceCount || 0))
      },
      memoryUsage: {
        jsp: jspPerf.memoryUsage?.usedJSHeapSize || 0,
        react: reactPerf.memoryUsage?.usedJSHeapSize || 0
      }
    };

    return comparison;
  }

  /**
   * 计算文本相似度
   */
  calculateTextSimilarity(arr1, arr2) {
    if (arr1.length === 0 && arr2.length === 0) return 1;
    if (arr1.length === 0 || arr2.length === 0) return 0;

    const set1 = new Set(arr1);
    const set2 = new Set(arr2);
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);

    return intersection.size / union.size;
  }

  /**
   * 计算链接相似度
   */
  calculateLinkSimilarity(jspLinks, reactLinks) {
    if (jspLinks.length === 0 && reactLinks.length === 0) return 1;
    if (jspLinks.length === 0 || reactLinks.length === 0) return 0;

    const jspTexts = jspLinks.map(link => link.text?.toLowerCase().trim()).filter(Boolean);
    const reactTexts = reactLinks.map(link => link.text?.toLowerCase().trim()).filter(Boolean);

    return this.calculateTextSimilarity(jspTexts, reactTexts);
  }

  /**
   * 计算可访问性分数
   */
  calculateAccessibilityScore(elements) {
    let score = 1.0;

    // 检查图片alt属性
    if (elements.images && elements.images.length > 0) {
      const missingAlt = elements.altMissingImages || 0;
      score -= (missingAlt / elements.images.length) * 0.3;
    }

    // 检查空链接
    if (elements.links && elements.links.length > 0) {
      const emptyLinks = elements.emptyLinks || 0;
      score -= (emptyLinks / elements.links.length) * 0.2;
    }

    // 检查标题结构
    if (elements.headings && elements.headings.length > 0) {
      const hasH1 = elements.headings.some(h => h.tag === 'h1');
      if (!hasH1) score -= 0.1;
    }

    return Math.max(0, score);
  }

  /**
   * 生成比较摘要
   */
  generateComparisonSummary(comparison) {
    const { score } = comparison;

    // 根据分数生成总体评价
    if (score.overall >= 0.9) {
      comparison.summary = '转换质量优秀，页面高度相似';
      comparison.level = 'excellent';
    } else if (score.overall >= 0.7) {
      comparison.summary = '转换质量良好，存在少量差异';
      comparison.level = 'good';
    } else if (score.overall >= 0.5) {
      comparison.summary = '转换质量一般，存在明显差异';
      comparison.level = 'fair';
    } else {
      comparison.summary = '转换质量较差，需要改进';
      comparison.level = 'poor';
    }

    // 收集所有相似点和差异点
    Object.values(comparison.detailedComparison).forEach(detail => {
      if (detail.similarities) {
        comparison.similarities.push(...detail.similarities);
      }
      if (detail.differences) {
        comparison.differences.push(...detail.differences);
      }
    });
  }

  /**
   * 截图对比
   */
  async compareScreenshots(jspScreenshot, reactScreenshot, identifier) {
    const comparison = {
      jspScreenshot,
      reactScreenshot,
      similarity: 0,
      differences: [],
      diffImage: null,
      analysis: {},
      pixelDifferences: 0,
      totalPixels: 0
    };

    try {
      // 检查文件是否存在
      if (!await fs.pathExists(jspScreenshot) || !await fs.pathExists(reactScreenshot)) {
        comparison.differences.push('截图文件不存在');
        return comparison;
      }

      console.log(chalk.gray(`  🔍 进行像素级截图对比...`));

      // 读取图像文件
      const jspBuffer = await fs.readFile(jspScreenshot);
      const reactBuffer = await fs.readFile(reactScreenshot);

      // 解析 PNG 图像
      const jspPng = PNG.sync.read(jspBuffer);
      const reactPng = PNG.sync.read(reactBuffer);

      // 检查图像尺寸
      if (jspPng.width !== reactPng.width || jspPng.height !== reactPng.height) {
        // 如果尺寸不同，需要调整到相同尺寸
        const targetWidth = Math.max(jspPng.width, reactPng.width);
        const targetHeight = Math.max(jspPng.height, reactPng.height);

        comparison.differences.push(`图像尺寸不同: JSP(${jspPng.width}x${jspPng.height}) vs React(${reactPng.width}x${reactPng.height})`);

        // 创建调整后的图像
        const resizedJsp = this.resizeImage(jspPng, targetWidth, targetHeight);
        const resizedReact = this.resizeImage(reactPng, targetWidth, targetHeight);

        return this.compareResizedImages(resizedJsp, resizedReact, identifier, comparison);
      }

      // 创建差异图像
      const { width, height } = jspPng;
      const diffPng = new PNG({ width, height });

      // 进行像素对比
      const pixelDifferences = pixelmatch(
        jspPng.data,
        reactPng.data,
        diffPng.data,
        width,
        height,
        {
          threshold: 0.1, // 像素差异阈值
          alpha: 0.2,     // 透明度
          antialiasing: false,
          diffColor: [255, 0, 255], // 差异颜色（洋红色）
          diffColorAlt: [0, 255, 255] // 替代差异颜色（青色）
        }
      );

      comparison.pixelDifferences = pixelDifferences;
      comparison.totalPixels = width * height;
      comparison.similarity = 1 - (pixelDifferences / comparison.totalPixels);

      // 保存差异图像
      const diffImagePath = path.join(
        this.options.screenshotDir,
        `diff-${identifier}-${Date.now()}.png`
      );

      const diffBuffer = PNG.sync.write(diffPng);
      await fs.writeFile(diffImagePath, diffBuffer);
      comparison.diffImage = diffImagePath;

      // 分析结果
      comparison.analysis = {
        jspDimensions: { width: jspPng.width, height: jspPng.height },
        reactDimensions: { width: reactPng.width, height: reactPng.height },
        pixelDifferencePercentage: (pixelDifferences / comparison.totalPixels) * 100,
        similarityPercentage: comparison.similarity * 100
      };

      // 生成差异描述
      if (comparison.similarity > 0.95) {
        comparison.differences.push('图像几乎完全相同');
      } else if (comparison.similarity > 0.8) {
        comparison.differences.push('图像高度相似，存在少量差异');
      } else if (comparison.similarity > 0.6) {
        comparison.differences.push('图像相似，存在明显差异');
      } else {
        comparison.differences.push('图像差异较大');
      }

      console.log(chalk.green(`  ✅ 截图对比完成: 相似度 ${(comparison.similarity * 100).toFixed(1)}%`));

    } catch (error) {
      comparison.differences.push(`截图对比失败: ${error.message}`);
      comparison.similarity = 0;
      console.log(chalk.red(`  ❌ 截图对比失败: ${error.message}`));
    }

    return comparison;
  }

  /**
   * 调整图像尺寸
   */
  resizeImage(png, targetWidth, targetHeight) {
    const resized = new PNG({ width: targetWidth, height: targetHeight });

    // 简单的图像复制（不进行缩放，只是填充）
    for (let y = 0; y < targetHeight; y++) {
      for (let x = 0; x < targetWidth; x++) {
        const srcX = Math.min(x, png.width - 1);
        const srcY = Math.min(y, png.height - 1);

        const srcIdx = (png.width * srcY + srcX) << 2;
        const dstIdx = (targetWidth * y + x) << 2;

        if (x < png.width && y < png.height) {
          resized.data[dstIdx] = png.data[srcIdx];     // R
          resized.data[dstIdx + 1] = png.data[srcIdx + 1]; // G
          resized.data[dstIdx + 2] = png.data[srcIdx + 2]; // B
          resized.data[dstIdx + 3] = png.data[srcIdx + 3]; // A
        } else {
          // 填充白色背景
          resized.data[dstIdx] = 255;     // R
          resized.data[dstIdx + 1] = 255; // G
          resized.data[dstIdx + 2] = 255; // B
          resized.data[dstIdx + 3] = 255; // A
        }
      }
    }

    return resized;
  }

  /**
   * 比较调整后的图像
   */
  async compareResizedImages(jspPng, reactPng, identifier, comparison) {
    const { width, height } = jspPng;
    const diffPng = new PNG({ width, height });

    const pixelDifferences = pixelmatch(
      jspPng.data,
      reactPng.data,
      diffPng.data,
      width,
      height,
      { threshold: 0.1 }
    );

    comparison.pixelDifferences = pixelDifferences;
    comparison.totalPixels = width * height;
    comparison.similarity = 1 - (pixelDifferences / comparison.totalPixels);

    // 保存差异图像
    const diffImagePath = path.join(
      this.options.screenshotDir,
      `diff-${identifier}-${Date.now()}.png`
    );

    const diffBuffer = PNG.sync.write(diffPng);
    await fs.writeFile(diffImagePath, diffBuffer);
    comparison.diffImage = diffImagePath;

    comparison.analysis.pixelDifferencePercentage = (pixelDifferences / comparison.totalPixels) * 100;
    comparison.analysis.similarityPercentage = comparison.similarity * 100;

    return comparison;
  }

  /**
   * 验证嵌入式版本
   */
  async validateEmbeddedVersion(conversionResult) {
    // 这里可以验证嵌入式 JSP 页面
    // 暂时返回基本信息
    return {
      embeddedPath: conversionResult.embeddedPath,
      validated: false,
      note: '嵌入式验证需要运行的 JSP 服务器'
    };
  }

  /**
   * 获取 JSP 路径
   */
  getJSPPath(relativePath) {
    // 移除 .jsp 扩展名，因为通常 URL 不包含扩展名
    return relativePath.replace('.jsp', '');
  }

  /**
   * 获取 React 路径
   */
  getReactPath(componentName) {
    // 根据组件名生成对应的路由
    const routeMap = {
      'Posts': '',
      'Post': 'post',
      'Create': 'create',
      'Edit': 'edit'
    };
    
    return routeMap[componentName] || componentName.toLowerCase();
  }

  /**
   * 生成验证报告
   */
  async generateValidationReport() {
    const reportPath = path.join(this.options.screenshotDir, 'validation-report.json');
    const htmlReportPath = path.join(this.options.screenshotDir, 'validation-report.html');

    // 计算统计信息
    const stats = this.calculateValidationStats();

    const report = {
      timestamp: new Date().toISOString(),
      configuration: {
        jspBaseUrl: this.options.jspBaseUrl,
        reactBaseUrl: this.options.reactBaseUrl,
        performanceMonitoring: this.options.performanceMonitoring,
        visualComparison: this.options.visualComparison,
        thresholds: {
          performance: this.options.performanceThresholds,
          visual: this.options.visualThresholds
        }
      },
      summary: {
        total: this.validationResults.length,
        successful: this.validationResults.filter(r => r.success).length,
        failed: this.validationResults.filter(r => !r.success).length,
        averageStructureSimilarity: stats.averageStructureSimilarity,
        averageVisualSimilarity: stats.averageVisualSimilarity,
        averageLoadTime: stats.averageLoadTime,
        totalErrors: stats.totalErrors,
        totalWarnings: stats.totalWarnings
      },
      statistics: stats,
      results: this.validationResults
    };

    // 保存 JSON 报告
    await fs.writeJson(reportPath, report, { spaces: 2 });

    // 生成 HTML 报告
    await this.generateHTMLReport(report, htmlReportPath);

    console.log(chalk.blue(`📊 验证报告已生成:`));
    console.log(chalk.gray(`  JSON: ${reportPath}`));
    console.log(chalk.gray(`  HTML: ${htmlReportPath}`));

    // 打印详细摘要
    this.printValidationSummary(report);
  }

  /**
   * 生成 HTML 报告
   */
  async generateHTMLReport(report, htmlPath) {
    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSP2React 验证报告</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: white; padding: 30px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .title { font-size: 2.5em; color: #2c3e50; margin-bottom: 10px; }
        .subtitle { color: #7f8c8d; font-size: 1.1em; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stat-number { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .stat-label { color: #7f8c8d; font-size: 0.9em; }
        .success { color: #27ae60; }
        .warning { color: #f39c12; }
        .error { color: #e74c3c; }
        .info { color: #3498db; }
        .results { background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .result-item { border-bottom: 1px solid #ecf0f1; padding: 20px; }
        .result-item:last-child { border-bottom: none; }
        .result-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .result-title { font-size: 1.2em; font-weight: bold; }
        .result-status { padding: 5px 10px; border-radius: 4px; font-size: 0.8em; font-weight: bold; text-transform: uppercase; }
        .status-success { background: #d5f4e6; color: #27ae60; }
        .status-failed { background: #fadbd8; color: #e74c3c; }
        .result-details { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .detail-section { background: #f8f9fa; padding: 15px; border-radius: 4px; }
        .detail-title { font-weight: bold; margin-bottom: 10px; color: #2c3e50; }
        .screenshot-container { display: flex; gap: 10px; flex-wrap: wrap; }
        .screenshot { max-width: 200px; border-radius: 4px; border: 1px solid #ddd; }
        .comparison-score { font-size: 1.5em; font-weight: bold; text-align: center; padding: 10px; border-radius: 4px; }
        .score-excellent { background: #d5f4e6; color: #27ae60; }
        .score-good { background: #fff3cd; color: #856404; }
        .score-fair { background: #f8d7da; color: #721c24; }
        .score-poor { background: #f5c6cb; color: #721c24; }
        .performance-metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; }
        .metric { text-align: center; padding: 10px; background: white; border-radius: 4px; }
        .metric-value { font-size: 1.2em; font-weight: bold; }
        .metric-label { font-size: 0.8em; color: #7f8c8d; }
        .error-list { max-height: 200px; overflow-y: auto; }
        .error-item { padding: 5px 0; border-bottom: 1px solid #eee; font-size: 0.9em; }
        .toggle-btn { background: #3498db; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; margin-top: 10px; }
        .toggle-btn:hover { background: #2980b9; }
        .hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">JSP2React 验证报告</h1>
            <p class="subtitle">生成时间: ${new Date(report.timestamp).toLocaleString('zh-CN')}</p>
        </div>

        <div class="summary">
            <div class="stat-card">
                <div class="stat-number info">${report.summary.total}</div>
                <div class="stat-label">总验证数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number success">${report.summary.successful}</div>
                <div class="stat-label">成功</div>
            </div>
            <div class="stat-card">
                <div class="stat-number error">${report.summary.failed}</div>
                <div class="stat-label">失败</div>
            </div>
            <div class="stat-card">
                <div class="stat-number info">${report.summary.averageStructureSimilarity?.toFixed(1) || 0}%</div>
                <div class="stat-label">平均结构相似度</div>
            </div>
            ${report.summary.averageVisualSimilarity ? `
            <div class="stat-card">
                <div class="stat-number info">${report.summary.averageVisualSimilarity.toFixed(1)}%</div>
                <div class="stat-label">平均视觉相似度</div>
            </div>
            ` : ''}
            <div class="stat-card">
                <div class="stat-number warning">${report.summary.totalErrors}</div>
                <div class="stat-label">总错误数</div>
            </div>
        </div>

        <div class="results">
            ${report.results.map((result, index) => this.generateResultHTML(result, index)).join('')}
        </div>
    </div>

    <script>
        function toggleDetails(index) {
            const details = document.getElementById('details-' + index);
            const btn = document.getElementById('btn-' + index);
            if (details.classList.contains('hidden')) {
                details.classList.remove('hidden');
                btn.textContent = '隐藏详情';
            } else {
                details.classList.add('hidden');
                btn.textContent = '显示详情';
            }
        }
    </script>
</body>
</html>`;

    await fs.writeFile(htmlPath, html);
  }

  /**
   * 生成单个结果的 HTML
   */
  generateResultHTML(result, index) {
    const statusClass = result.success ? 'status-success' : 'status-failed';
    const statusText = result.success ? '成功' : '失败';

    const structureSimilarity = result.results?.comparison?.structureSimilarity || 0;
    const visualSimilarity = result.results?.visualComparison?.similarity ?
      (result.results.visualComparison.similarity * 100) : null;

    const getScoreClass = (score) => {
      if (score >= 90) return 'score-excellent';
      if (score >= 70) return 'score-good';
      if (score >= 50) return 'score-fair';
      return 'score-poor';
    };

    return `
        <div class="result-item">
            <div class="result-header">
                <div class="result-title">${result.fileName} → ${result.componentName}</div>
                <div class="result-status ${statusClass}">${statusText}</div>
            </div>

            <div class="result-details">
                <div class="detail-section">
                    <div class="detail-title">结构对比</div>
                    <div class="comparison-score ${getScoreClass(structureSimilarity)}">
                        ${structureSimilarity.toFixed(1)}% 相似度
                    </div>
                </div>

                ${visualSimilarity ? `
                <div class="detail-section">
                    <div class="detail-title">视觉对比</div>
                    <div class="comparison-score ${getScoreClass(visualSimilarity)}">
                        ${visualSimilarity.toFixed(1)}% 相似度
                    </div>
                </div>
                ` : ''}

                ${result.results?.jsp?.performance || result.results?.react?.performance ? `
                <div class="detail-section">
                    <div class="detail-title">性能指标</div>
                    <div class="performance-metrics">
                        ${result.results.jsp?.loadTime ? `
                        <div class="metric">
                            <div class="metric-value">${result.results.jsp.loadTime}ms</div>
                            <div class="metric-label">JSP 加载时间</div>
                        </div>
                        ` : ''}
                        ${result.results.react?.loadTime ? `
                        <div class="metric">
                            <div class="metric-value">${result.results.react.loadTime}ms</div>
                            <div class="metric-label">React 加载时间</div>
                        </div>
                        ` : ''}
                    </div>
                </div>
                ` : ''}

                ${result.results?.jsp?.screenshot || result.results?.react?.screenshot ? `
                <div class="detail-section">
                    <div class="detail-title">截图对比</div>
                    <div class="screenshot-container">
                        ${result.results.jsp?.screenshot ? `
                        <div>
                            <p>JSP 页面</p>
                            <img src="${path.basename(result.results.jsp.screenshot)}" alt="JSP Screenshot" class="screenshot">
                        </div>
                        ` : ''}
                        ${result.results.react?.screenshot ? `
                        <div>
                            <p>React 页面</p>
                            <img src="${path.basename(result.results.react.screenshot)}" alt="React Screenshot" class="screenshot">
                        </div>
                        ` : ''}
                        ${result.results.visualComparison?.diffImage ? `
                        <div>
                            <p>差异图像</p>
                            <img src="${path.basename(result.results.visualComparison.diffImage)}" alt="Diff Image" class="screenshot">
                        </div>
                        ` : ''}
                    </div>
                </div>
                ` : ''}
            </div>

            <button class="toggle-btn" id="btn-${index}" onclick="toggleDetails(${index})">显示详情</button>

            <div id="details-${index}" class="hidden">
                ${this.generateDetailedResultHTML(result)}
            </div>
        </div>
    `;
  }

  /**
   * 生成详细结果 HTML
   */
  generateDetailedResultHTML(result) {
    let html = '<div style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 4px;">';

    // 错误信息
    const allErrors = [
      ...(result.results?.jsp?.errors || []),
      ...(result.results?.react?.errors || [])
    ];

    if (allErrors.length > 0) {
      html += `
        <div class="detail-section">
            <div class="detail-title">错误信息 (${allErrors.length})</div>
            <div class="error-list">
                ${allErrors.map(error => `
                    <div class="error-item">
                        ${typeof error === 'string' ? error : error.message || JSON.stringify(error)}
                    </div>
                `).join('')}
            </div>
        </div>
      `;
    }

    // 警告信息
    const allWarnings = [
      ...(result.results?.jsp?.warnings || []),
      ...(result.results?.react?.warnings || [])
    ];

    if (allWarnings.length > 0) {
      html += `
        <div class="detail-section">
            <div class="detail-title">警告信息 (${allWarnings.length})</div>
            <div class="error-list">
                ${allWarnings.map(warning => `
                    <div class="error-item" style="color: #856404;">
                        ${warning}
                    </div>
                `).join('')}
            </div>
        </div>
      `;
    }

    // 详细对比信息
    if (result.results?.comparison?.detailedComparison) {
      html += `
        <div class="detail-section">
            <div class="detail-title">详细对比分析</div>
            <pre style="background: white; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 0.8em;">
${JSON.stringify(result.results.comparison.detailedComparison, null, 2)}
            </pre>
        </div>
      `;
    }

    html += '</div>';
    return html;
  }

  /**
   * 计算验证统计信息
   */
  calculateValidationStats() {
    const stats = {
      averageStructureSimilarity: 0,
      averageVisualSimilarity: 0,
      averageLoadTime: { jsp: 0, react: 0 },
      totalErrors: 0,
      totalWarnings: 0,
      performanceStats: {},
      errorCategories: {},
      accessibilityIssues: 0,
      visualComparisonStats: {
        totalComparisons: 0,
        averagePixelDifference: 0,
        excellentMatches: 0, // >95%
        goodMatches: 0,      // 80-95%
        fairMatches: 0,      // 60-80%
        poorMatches: 0       // <60%
      }
    };

    if (this.validationResults.length === 0) return stats;

    let totalSimilarity = 0;
    let totalVisualSimilarity = 0;
    let totalJspLoadTime = 0;
    let totalReactLoadTime = 0;
    let validSimilarityCount = 0;
    let validVisualSimilarityCount = 0;
    let validJspLoadTimeCount = 0;
    let validReactLoadTimeCount = 0;
    let totalPixelDifference = 0;

    this.validationResults.forEach(result => {
      // 结构相似度
      if (result.results?.comparison?.structureSimilarity) {
        totalSimilarity += result.results.comparison.structureSimilarity;
        validSimilarityCount++;
      }

      // 视觉相似度
      if (result.results?.visualComparison?.similarity) {
        const visualSim = result.results.visualComparison.similarity * 100;
        totalVisualSimilarity += visualSim;
        validVisualSimilarityCount++;

        // 统计视觉匹配质量
        stats.visualComparisonStats.totalComparisons++;
        if (result.results.visualComparison.pixelDifferences && result.results.visualComparison.totalPixels) {
          const pixelDiffPercentage = (result.results.visualComparison.pixelDifferences / result.results.visualComparison.totalPixels) * 100;
          totalPixelDifference += pixelDiffPercentage;
        }

        if (visualSim >= 95) {
          stats.visualComparisonStats.excellentMatches++;
        } else if (visualSim >= 80) {
          stats.visualComparisonStats.goodMatches++;
        } else if (visualSim >= 60) {
          stats.visualComparisonStats.fairMatches++;
        } else {
          stats.visualComparisonStats.poorMatches++;
        }
      }

      // 加载时间
      if (result.results?.jsp?.loadTime) {
        totalJspLoadTime += result.results.jsp.loadTime;
        validJspLoadTimeCount++;
      }
      if (result.results?.react?.loadTime) {
        totalReactLoadTime += result.results.react.loadTime;
        validReactLoadTimeCount++;
      }

      // 错误和警告统计
      if (result.results?.jsp?.errors) {
        stats.totalErrors += result.results.jsp.errors.length;
      }
      if (result.results?.react?.errors) {
        stats.totalErrors += result.results.react.errors.length;
      }
      if (result.results?.jsp?.warnings) {
        stats.totalWarnings += result.results.jsp.warnings.length;
      }
      if (result.results?.react?.warnings) {
        stats.totalWarnings += result.results.react.warnings.length;
      }

      // 错误分类统计
      [result.results?.jsp?.errors, result.results?.react?.errors].forEach(errors => {
        if (Array.isArray(errors)) {
          errors.forEach(error => {
            const category = error.category || error.type || 'unknown';
            stats.errorCategories[category] = (stats.errorCategories[category] || 0) + 1;
          });
        }
      });
    });

    // 计算平均值
    stats.averageStructureSimilarity = validSimilarityCount > 0 ? totalSimilarity / validSimilarityCount : 0;
    stats.averageVisualSimilarity = validVisualSimilarityCount > 0 ? totalVisualSimilarity / validVisualSimilarityCount : 0;
    stats.averageLoadTime.jsp = validJspLoadTimeCount > 0 ? totalJspLoadTime / validJspLoadTimeCount : 0;
    stats.averageLoadTime.react = validReactLoadTimeCount > 0 ? totalReactLoadTime / validReactLoadTimeCount : 0;

    if (stats.visualComparisonStats.totalComparisons > 0) {
      stats.visualComparisonStats.averagePixelDifference = totalPixelDifference / stats.visualComparisonStats.totalComparisons;
    }

    return stats;
  }

  /**
   * 打印验证摘要
   */
  printValidationSummary(report) {
    const { summary, statistics } = report;

    console.log(chalk.blue('\n📋 详细验证摘要:'));
    console.log(`总计: ${summary.total}`);
    console.log(chalk.green(`成功: ${summary.successful}`));
    console.log(chalk.red(`失败: ${summary.failed}`));

    if (summary.averageStructureSimilarity > 0) {
      const similarityColor = summary.averageStructureSimilarity > 80 ? chalk.green :
                             summary.averageStructureSimilarity > 60 ? chalk.yellow : chalk.red;
      console.log(similarityColor(`平均结构相似度: ${summary.averageStructureSimilarity.toFixed(1)}%`));
    }

    if (summary.averageLoadTime) {
      console.log(chalk.gray(`平均加载时间:`));
      console.log(chalk.gray(`  JSP: ${summary.averageLoadTime.jsp.toFixed(0)}ms`));
      console.log(chalk.gray(`  React: ${summary.averageLoadTime.react.toFixed(0)}ms`));
    }

    if (summary.totalErrors > 0) {
      console.log(chalk.red(`总错误数: ${summary.totalErrors}`));
    }

    if (summary.totalWarnings > 0) {
      console.log(chalk.yellow(`总警告数: ${summary.totalWarnings}`));
    }

    // 显示错误分类
    if (Object.keys(statistics.errorCategories).length > 0) {
      console.log(chalk.blue('\n🔍 错误分类:'));
      Object.entries(statistics.errorCategories).forEach(([category, count]) => {
        console.log(chalk.gray(`  ${category}: ${count}`));
      });
    }
  }

  /**
   * 关闭浏览器
   */
  async close() {
    if (this.browser) {
      await this.browser.close();
      console.log(chalk.gray('🔒 Puppeteer 浏览器已关闭'));
    }
  }
}

module.exports = { PuppeteerValidator };
