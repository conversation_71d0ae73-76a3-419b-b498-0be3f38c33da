const puppeteer = require('puppeteer');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * Puppeteer 验证工具
 * 使用 Puppeteer 验证 JSP 转 React 的转换结果
 * 包含页面可访问性、错误监控、结构对比、截图对比和性能监控功能
 */
class PuppeteerValidator {
  constructor(options = {}) {
    this.options = {
      jspBaseUrl: options.jspBaseUrl || 'http://localhost:8080',
      reactBaseUrl: options.reactBaseUrl || 'http://localhost:3000',
      headless: options.headless !== false,
      timeout: options.timeout || 30000,
      viewport: options.viewport || { width: 1280, height: 720 },
      screenshotDir: options.screenshotDir || './screenshots',
      verbose: options.verbose || false,
      // 新增配置选项
      performanceMonitoring: options.performanceMonitoring !== false,
      visualComparison: options.visualComparison !== false,
      errorAnalysis: options.errorAnalysis !== false,
      structureAnalysis: options.structureAnalysis !== false,
      // 性能阈值配置
      performanceThresholds: {
        loadTime: options.performanceThresholds?.loadTime || 5000, // 5秒
        firstContentfulPaint: options.performanceThresholds?.firstContentfulPaint || 2000, // 2秒
        largestContentfulPaint: options.performanceThresholds?.largestContentfulPaint || 4000, // 4秒
        cumulativeLayoutShift: options.performanceThresholds?.cumulativeLayoutShift || 0.1,
        firstInputDelay: options.performanceThresholds?.firstInputDelay || 100, // 100ms
        ...options.performanceThresholds
      },
      // 视觉对比阈值
      visualThresholds: {
        pixelDifferenceThreshold: options.visualThresholds?.pixelDifferenceThreshold || 0.1, // 10%
        structureSimilarityThreshold: options.visualThresholds?.structureSimilarityThreshold || 0.8, // 80%
        ...options.visualThresholds
      },
      ...options
    };

    this.browser = null;
    this.validationResults = [];
    this.performanceMetrics = [];
  }

  /**
   * 初始化 Puppeteer
   */
  async initialize() {
    console.log(chalk.blue('🚀 启动 Puppeteer 浏览器...'));
    
    this.browser = await puppeteer.launch({
      headless: this.options.headless,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    });

    await fs.ensureDir(this.options.screenshotDir);
    console.log(chalk.green('✅ Puppeteer 浏览器已启动'));
  }

  /**
   * 验证转换结果
   */
  async validateConversion(conversionResults) {
    if (!this.browser) {
      await this.initialize();
    }

    console.log(chalk.blue('🔍 开始验证转换结果...'));

    for (const result of conversionResults) {
      if (result.reactCode && result.file) {
        await this.validateSingleConversion(result);
      }
    }

    await this.generateValidationReport();
    console.log(chalk.green('✅ 验证完成'));

    return this.validationResults;
  }

  /**
   * 验证单个转换结果
   */
  async validateSingleConversion(conversionResult) {
    const { file, componentName } = conversionResult;
    const jspPath = this.getJSPPath(file.relativePath);
    
    console.log(chalk.gray(`🔍 验证 ${file.name} -> ${componentName}...`));

    const validation = {
      fileName: file.name,
      componentName,
      jspUrl: `${this.options.jspBaseUrl}/${jspPath}`,
      reactUrl: `${this.options.reactBaseUrl}/${this.getReactPath(componentName)}`,
      timestamp: new Date().toISOString(),
      results: {}
    };

    try {
      // 1. 验证原始 JSP 页面
      validation.results.jsp = await this.validateJSPPage(validation.jspUrl, file.name);

      // 2. 验证转换后的 React 页面
      validation.results.react = await this.validateReactPage(validation.reactUrl, componentName);

      // 3. 比较页面结构
      validation.results.comparison = await this.comparePages(validation.results.jsp, validation.results.react);

      // 4. 视觉对比
      if (this.options.visualComparison && validation.results.jsp.screenshot && validation.results.react.screenshot) {
        validation.results.visualComparison = await this.compareScreenshots(
          validation.results.jsp.screenshot,
          validation.results.react.screenshot,
          `${file.name}-${componentName}`
        );
      }

      // 5. 验证嵌入式版本
      validation.results.embedded = await this.validateEmbeddedVersion(conversionResult);

      validation.success = true;

    } catch (error) {
      validation.error = error.message;
      validation.success = false;
      console.error(chalk.red(`❌ 验证失败 ${file.name}: ${error.message}`));
    }

    this.validationResults.push(validation);
  }

  /**
   * 验证 JSP 页面
   */
  async validateJSPPage(url, fileName) {
    const page = await this.browser.newPage();
    await page.setViewport(this.options.viewport);

    const result = {
      url,
      accessible: false,
      errors: [],
      warnings: [],
      screenshot: null,
      content: null,
      elements: {},
      performance: {},
      loadTime: 0
    };

    try {
      // 启用性能监控
      if (this.options.performanceMonitoring) {
        await page.setCacheEnabled(false);
        await page.setRequestInterception(true);

        // 监听网络请求
        page.on('request', request => {
          request.continue();
        });
      }

      // 监听控制台错误和警告
      const consoleMessages = [];
      page.on('console', msg => {
        const message = {
          type: msg.type(),
          text: msg.text(),
          location: msg.location(),
          timestamp: new Date().toISOString()
        };
        consoleMessages.push(message);

        if (msg.type() === 'error') {
          result.errors.push(this.categorizeError(msg.text()));
        } else if (msg.type() === 'warning') {
          result.warnings.push(msg.text());
        }
      });

      // 监听页面错误
      page.on('pageerror', error => {
        result.errors.push({
          type: 'page_error',
          message: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString()
        });
      });

      // 监听请求失败
      const failedRequests = [];
      page.on('requestfailed', request => {
        const failedRequest = {
          url: request.url(),
          method: request.method(),
          errorText: request.failure().errorText,
          timestamp: new Date().toISOString()
        };
        failedRequests.push(failedRequest);
        result.errors.push(`Request Failed: ${request.url()} - ${request.failure().errorText}`);
      });

      console.log(chalk.gray(`  📄 访问 JSP 页面: ${url}`));

      // 记录开始时间
      const startTime = Date.now();

      const response = await page.goto(url, {
        waitUntil: 'networkidle0',
        timeout: this.options.timeout
      });

      // 记录加载时间
      result.loadTime = Date.now() - startTime;

      if (response && response.ok()) {
        result.accessible = true;
        result.statusCode = response.status();

        // 等待页面完全加载
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 收集性能指标
        if (this.options.performanceMonitoring) {
          result.performance = await this.collectPerformanceMetrics(page);
        }

        // 获取页面内容
        result.content = await page.content();

        // 分析页面元素
        result.elements = await this.analyzePageElements(page);

        // 截图
        const screenshotPath = path.join(
          this.options.screenshotDir,
          `jsp-${fileName.replace('.jsp', '')}-${Date.now()}.png`
        );
        await page.screenshot({ path: screenshotPath, fullPage: true });
        result.screenshot = screenshotPath;

        // 保存控制台消息和失败请求
        result.consoleMessages = consoleMessages;
        result.failedRequests = failedRequests;

        console.log(chalk.green(`  ✅ JSP 页面访问成功 (${result.loadTime}ms)`));

      } else {
        result.errors.push({
          type: 'http_error',
          message: `HTTP ${response.status()}: ${response.statusText()}`,
          statusCode: response.status(),
          timestamp: new Date().toISOString()
        });
      }

    } catch (error) {
      result.errors.push(`Navigation Error: ${error.message}`);
      console.log(chalk.yellow(`  ⚠️  JSP 页面访问失败: ${error.message}`));
    } finally {
      await page.close();
    }

    return result;
  }

  /**
   * 验证 React 页面
   */
  async validateReactPage(url, componentName) {
    const page = await this.browser.newPage();
    await page.setViewport(this.options.viewport);

    const result = {
      url,
      accessible: false,
      errors: [],
      warnings: [],
      screenshot: null,
      content: null,
      elements: {},
      reactErrors: [],
      performance: {},
      loadTime: 0
    };

    try {
      // 启用性能监控
      if (this.options.performanceMonitoring) {
        await page.setCacheEnabled(false);
        await page.setRequestInterception(true);

        page.on('request', request => {
          request.continue();
        });
      }

      // 监听 React 错误
      await page.evaluateOnNewDocument(() => {
        window.reactErrors = [];
        window.consoleMessages = [];

        const originalError = console.error;
        const originalWarn = console.warn;

        console.error = (...args) => {
          const message = args.join(' ');
          window.consoleMessages.push({ type: 'error', message, timestamp: Date.now() });
          if (message.includes('React') || message.includes('Warning:') || message.includes('Component')) {
            window.reactErrors.push(message);
          }
          originalError.apply(console, args);
        };

        console.warn = (...args) => {
          const message = args.join(' ');
          window.consoleMessages.push({ type: 'warning', message, timestamp: Date.now() });
          originalWarn.apply(console, args);
        };
      });

      // 监听控制台消息
      const consoleMessages = [];
      page.on('console', msg => {
        const message = {
          type: msg.type(),
          text: msg.text(),
          location: msg.location(),
          timestamp: new Date().toISOString()
        };
        consoleMessages.push(message);

        if (msg.type() === 'error') {
          result.errors.push(this.categorizeError(msg.text()));
        } else if (msg.type() === 'warning') {
          result.warnings.push(msg.text());
        }
      });

      // 监听页面错误
      page.on('pageerror', error => {
        result.errors.push({
          type: 'page_error',
          message: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString()
        });
      });

      // 监听请求失败
      const failedRequests = [];
      page.on('requestfailed', request => {
        const failedRequest = {
          url: request.url(),
          method: request.method(),
          errorText: request.failure().errorText,
          timestamp: new Date().toISOString()
        };
        failedRequests.push(failedRequest);
        result.errors.push(`Request Failed: ${request.url()} - ${request.failure().errorText}`);
      });

      console.log(chalk.gray(`  ⚛️  访问 React 页面: ${url}`));

      // 记录开始时间
      const startTime = Date.now();

      const response = await page.goto(url, {
        waitUntil: 'networkidle0',
        timeout: this.options.timeout
      });

      // 记录加载时间
      result.loadTime = Date.now() - startTime;

      if (response && response.ok()) {
        result.accessible = true;
        result.statusCode = response.status();

        // 等待 React 组件渲染
        await new Promise(resolve => setTimeout(resolve, 3000));

        // 检查 React 是否正确加载
        const reactInfo = await page.evaluate(() => {
          const info = {
            reactLoaded: typeof window.React !== 'undefined',
            reactDOMLoaded: typeof window.ReactDOM !== 'undefined',
            reactVersion: window.React ? window.React.version : null,
            reactDevTools: typeof window.__REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined'
          };

          // 检查是否有 React 组件挂载
          const reactRoots = document.querySelectorAll('[data-reactroot], #root, #app');
          info.hasReactRoots = reactRoots.length > 0;

          return info;
        });

        if (!reactInfo.reactLoaded) {
          result.warnings.push('React 库未正确加载');
        }

        if (!reactInfo.hasReactRoots) {
          result.warnings.push('未找到 React 根元素');
        }

        // 收集性能指标
        if (this.options.performanceMonitoring) {
          result.performance = await this.collectPerformanceMetrics(page);
        }

        // 获取 React 错误和控制台消息
        const pageData = await page.evaluate(() => ({
          reactErrors: window.reactErrors || [],
          consoleMessages: window.consoleMessages || []
        }));

        result.reactErrors = pageData.reactErrors;
        result.reactInfo = reactInfo;

        // 获取页面内容
        result.content = await page.content();

        // 分析页面元素
        result.elements = await this.analyzePageElements(page);

        // 截图
        const screenshotPath = path.join(
          this.options.screenshotDir,
          `react-${componentName}-${Date.now()}.png`
        );
        await page.screenshot({ path: screenshotPath, fullPage: true });
        result.screenshot = screenshotPath;

        // 保存控制台消息和失败请求
        result.consoleMessages = consoleMessages;
        result.failedRequests = failedRequests;

        console.log(chalk.green(`  ✅ React 页面访问成功 (${result.loadTime}ms)`));

      } else {
        result.errors.push({
          type: 'http_error',
          message: `HTTP ${response.status()}: ${response.statusText()}`,
          statusCode: response.status(),
          timestamp: new Date().toISOString()
        });
      }

    } catch (error) {
      result.errors.push(`Navigation Error: ${error.message}`);
      console.log(chalk.yellow(`  ⚠️  React 页面访问失败: ${error.message}`));
    } finally {
      await page.close();
    }

    return result;
  }

  /**
   * 收集性能指标
   */
  async collectPerformanceMetrics(page) {
    const metrics = await page.evaluate(() => {
      const performance = window.performance;
      const navigation = performance.getEntriesByType('navigation')[0];
      const paint = performance.getEntriesByType('paint');

      const result = {
        // 基本时间指标
        domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
        loadComplete: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,

        // 网络时间
        dnsLookup: navigation ? navigation.domainLookupEnd - navigation.domainLookupStart : 0,
        tcpConnect: navigation ? navigation.connectEnd - navigation.connectStart : 0,
        serverResponse: navigation ? navigation.responseEnd - navigation.requestStart : 0,

        // 渲染时间
        firstPaint: 0,
        firstContentfulPaint: 0,

        // 资源统计
        resourceCount: performance.getEntriesByType('resource').length,

        // 内存使用（如果可用）
        memoryUsage: window.performance.memory ? {
          usedJSHeapSize: window.performance.memory.usedJSHeapSize,
          totalJSHeapSize: window.performance.memory.totalJSHeapSize,
          jsHeapSizeLimit: window.performance.memory.jsHeapSizeLimit
        } : null
      };

      // 提取绘制时间
      paint.forEach(entry => {
        if (entry.name === 'first-paint') {
          result.firstPaint = entry.startTime;
        } else if (entry.name === 'first-contentful-paint') {
          result.firstContentfulPaint = entry.startTime;
        }
      });

      return result;
    });

    // 添加 Core Web Vitals（如果可用）
    try {
      const vitals = await page.evaluate(() => {
        return new Promise((resolve) => {
          if (typeof window.webVitals !== 'undefined') {
            const vitals = {};

            window.webVitals.getCLS(metric => vitals.cls = metric.value);
            window.webVitals.getFID(metric => vitals.fid = metric.value);
            window.webVitals.getLCP(metric => vitals.lcp = metric.value);

            setTimeout(() => resolve(vitals), 1000);
          } else {
            resolve({});
          }
        });
      });

      metrics.coreWebVitals = vitals;
    } catch (error) {
      metrics.coreWebVitals = { error: 'Core Web Vitals not available' };
    }

    return metrics;
  }

  /**
   * 错误分类
   */
  categorizeError(errorText) {
    const categories = {
      javascript: /javascript|js|script/i,
      network: /network|fetch|xhr|ajax|cors/i,
      react: /react|jsx|component|hook/i,
      css: /css|style|stylesheet/i,
      security: /security|csp|cors|mixed content/i,
      performance: /performance|timeout|slow/i
    };

    let category = 'unknown';
    for (const [cat, pattern] of Object.entries(categories)) {
      if (pattern.test(errorText)) {
        category = cat;
        break;
      }
    }

    return {
      type: 'console_error',
      category,
      message: errorText,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 分析页面元素
   */
  async analyzePageElements(page) {
    return await page.evaluate(() => {
      const elements = {
        title: document.title,
        headings: Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6')).map(h => ({
          tag: h.tagName.toLowerCase(),
          text: h.textContent.trim()
        })),
        forms: Array.from(document.querySelectorAll('form')).map(form => ({
          action: form.action,
          method: form.method,
          inputs: Array.from(form.querySelectorAll('input')).length
        })),
        links: Array.from(document.querySelectorAll('a[href]')).map(a => ({
          href: a.href,
          text: a.textContent.trim()
        })),
        images: Array.from(document.querySelectorAll('img')).map(img => ({
          src: img.src,
          alt: img.alt
        })),
        scripts: Array.from(document.querySelectorAll('script[src]')).map(script => script.src),
        styles: Array.from(document.querySelectorAll('link[rel="stylesheet"]')).map(link => link.href),

        // 新增：更详细的元素分析
        buttons: Array.from(document.querySelectorAll('button, input[type="button"], input[type="submit"]')).length,
        tables: Array.from(document.querySelectorAll('table')).length,
        lists: Array.from(document.querySelectorAll('ul, ol')).length,
        divs: Array.from(document.querySelectorAll('div')).length,

        // 文本内容统计
        textContent: document.body ? document.body.textContent.trim().length : 0,

        // 可访问性相关
        altMissingImages: Array.from(document.querySelectorAll('img:not([alt])')).length,
        emptyLinks: Array.from(document.querySelectorAll('a[href]:empty')).length
      };

      return elements;
    });
  }

  /**
   * 比较页面结构
   */
  async comparePages(jspResult, reactResult) {
    const comparison = {
      structureSimilarity: 0,
      differences: [],
      similarities: [],
      detailedComparison: {},
      score: {
        content: 0,
        structure: 0,
        functionality: 0,
        overall: 0
      }
    };

    if (!jspResult.accessible || !reactResult.accessible) {
      comparison.differences.push('其中一个页面无法访问');
      return comparison;
    }

    const jsp = jspResult.elements;
    const react = reactResult.elements;

    // 内容比较
    const contentComparison = this.compareContent(jsp, react);
    comparison.detailedComparison.content = contentComparison;
    comparison.score.content = contentComparison.score;

    // 结构比较
    const structureComparison = this.compareStructure(jsp, react);
    comparison.detailedComparison.structure = structureComparison;
    comparison.score.structure = structureComparison.score;

    // 功能比较
    const functionalityComparison = this.compareFunctionality(jsp, react);
    comparison.detailedComparison.functionality = functionalityComparison;
    comparison.score.functionality = functionalityComparison.score;

    // 性能比较
    if (jspResult.performance && reactResult.performance) {
      const performanceComparison = this.comparePerformance(jspResult.performance, reactResult.performance);
      comparison.detailedComparison.performance = performanceComparison;
    }

    // 计算总体相似度
    comparison.score.overall = (
      comparison.score.content * 0.4 +
      comparison.score.structure * 0.3 +
      comparison.score.functionality * 0.3
    );

    comparison.structureSimilarity = comparison.score.overall;

    // 生成摘要
    this.generateComparisonSummary(comparison);

    return comparison;
  }

  /**
   * 比较内容
   */
  compareContent(jsp, react) {
    const comparison = {
      score: 0,
      details: {},
      similarities: [],
      differences: []
    };

    let matches = 0;
    let total = 0;

    // 比较标题
    total++;
    if (jsp.title === react.title) {
      matches++;
      comparison.similarities.push('页面标题相同');
    } else {
      comparison.differences.push(`标题不同: JSP="${jsp.title}" vs React="${react.title}"`);
    }

    // 比较标题数量和内容
    total++;
    const jspHeadingTexts = jsp.headings.map(h => h.text).sort();
    const reactHeadingTexts = react.headings.map(h => h.text).sort();
    const headingSimilarity = this.calculateTextSimilarity(jspHeadingTexts, reactHeadingTexts);

    if (headingSimilarity > 0.8) {
      matches++;
      comparison.similarities.push(`标题内容相似度: ${(headingSimilarity * 100).toFixed(1)}%`);
    } else {
      comparison.differences.push(`标题内容差异较大: ${(headingSimilarity * 100).toFixed(1)}%`);
    }

    // 比较文本内容长度
    total++;
    const textLengthRatio = Math.min(jsp.textContent, react.textContent) / Math.max(jsp.textContent, react.textContent);
    if (textLengthRatio > 0.8) {
      matches++;
      comparison.similarities.push(`文本内容长度相近: ${textLengthRatio.toFixed(2)}`);
    } else {
      comparison.differences.push(`文本内容长度差异: JSP=${jsp.textContent} vs React=${react.textContent}`);
    }

    comparison.score = matches / total;
    comparison.details = {
      titleMatch: jsp.title === react.title,
      headingSimilarity,
      textLengthRatio,
      jspHeadings: jsp.headings.length,
      reactHeadings: react.headings.length
    };

    return comparison;
  }

  /**
   * 比较结构
   */
  compareStructure(jsp, react) {
    const comparison = {
      score: 0,
      details: {},
      similarities: [],
      differences: []
    };

    let matches = 0;
    let total = 0;

    // 比较各种元素数量
    const elements = ['forms', 'links', 'images', 'buttons', 'tables', 'lists'];

    elements.forEach(element => {
      total++;
      const jspCount = jsp[element]?.length || jsp[element] || 0;
      const reactCount = react[element]?.length || react[element] || 0;

      const ratio = jspCount === 0 && reactCount === 0 ? 1 :
                   Math.min(jspCount, reactCount) / Math.max(jspCount, reactCount);

      if (ratio > 0.8) {
        matches++;
        comparison.similarities.push(`${element}数量相近: JSP=${jspCount}, React=${reactCount}`);
      } else {
        comparison.differences.push(`${element}数量差异: JSP=${jspCount}, React=${reactCount}`);
      }
    });

    comparison.score = matches / total;
    comparison.details = {
      elementCounts: {
        jsp: elements.reduce((acc, el) => ({ ...acc, [el]: jsp[el]?.length || jsp[el] || 0 }), {}),
        react: elements.reduce((acc, el) => ({ ...acc, [el]: react[el]?.length || react[el] || 0 }), {})
      }
    };

    return comparison;
  }

  /**
   * 比较功能
   */
  compareFunctionality(jsp, react) {
    const comparison = {
      score: 0,
      details: {},
      similarities: [],
      differences: []
    };

    let matches = 0;
    let total = 0;

    // 比较表单功能
    total++;
    const jspForms = jsp.forms || [];
    const reactForms = react.forms || [];

    if (jspForms.length === reactForms.length) {
      matches++;
      comparison.similarities.push(`表单数量相同: ${jspForms.length}`);
    } else {
      comparison.differences.push(`表单数量不同: JSP=${jspForms.length} vs React=${reactForms.length}`);
    }

    // 比较链接功能
    total++;
    const jspLinks = jsp.links || [];
    const reactLinks = react.links || [];
    const linkSimilarity = this.calculateLinkSimilarity(jspLinks, reactLinks);

    if (linkSimilarity > 0.7) {
      matches++;
      comparison.similarities.push(`链接相似度: ${(linkSimilarity * 100).toFixed(1)}%`);
    } else {
      comparison.differences.push(`链接差异较大: ${(linkSimilarity * 100).toFixed(1)}%`);
    }

    // 比较可访问性
    total++;
    const jspAccessibility = this.calculateAccessibilityScore(jsp);
    const reactAccessibility = this.calculateAccessibilityScore(react);

    if (Math.abs(jspAccessibility - reactAccessibility) < 0.2) {
      matches++;
      comparison.similarities.push('可访问性水平相近');
    } else {
      comparison.differences.push(`可访问性差异: JSP=${jspAccessibility.toFixed(2)} vs React=${reactAccessibility.toFixed(2)}`);
    }

    comparison.score = matches / total;
    comparison.details = {
      formComparison: { jsp: jspForms.length, react: reactForms.length },
      linkSimilarity,
      accessibility: { jsp: jspAccessibility, react: reactAccessibility }
    };

    return comparison;
  }

  /**
   * 比较性能指标
   */
  comparePerformance(jspPerf, reactPerf) {
    const comparison = {
      loadTime: {
        jsp: jspPerf.loadComplete || 0,
        react: reactPerf.loadComplete || 0,
        difference: Math.abs((jspPerf.loadComplete || 0) - (reactPerf.loadComplete || 0)),
        winner: (jspPerf.loadComplete || 0) < (reactPerf.loadComplete || 0) ? 'JSP' : 'React'
      },
      firstContentfulPaint: {
        jsp: jspPerf.firstContentfulPaint || 0,
        react: reactPerf.firstContentfulPaint || 0,
        difference: Math.abs((jspPerf.firstContentfulPaint || 0) - (reactPerf.firstContentfulPaint || 0)),
        winner: (jspPerf.firstContentfulPaint || 0) < (reactPerf.firstContentfulPaint || 0) ? 'JSP' : 'React'
      },
      resourceCount: {
        jsp: jspPerf.resourceCount || 0,
        react: reactPerf.resourceCount || 0,
        difference: Math.abs((jspPerf.resourceCount || 0) - (reactPerf.resourceCount || 0))
      },
      memoryUsage: {
        jsp: jspPerf.memoryUsage?.usedJSHeapSize || 0,
        react: reactPerf.memoryUsage?.usedJSHeapSize || 0
      }
    };

    return comparison;
  }

  /**
   * 计算文本相似度
   */
  calculateTextSimilarity(arr1, arr2) {
    if (arr1.length === 0 && arr2.length === 0) return 1;
    if (arr1.length === 0 || arr2.length === 0) return 0;

    const set1 = new Set(arr1);
    const set2 = new Set(arr2);
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);

    return intersection.size / union.size;
  }

  /**
   * 计算链接相似度
   */
  calculateLinkSimilarity(jspLinks, reactLinks) {
    if (jspLinks.length === 0 && reactLinks.length === 0) return 1;
    if (jspLinks.length === 0 || reactLinks.length === 0) return 0;

    const jspTexts = jspLinks.map(link => link.text?.toLowerCase().trim()).filter(Boolean);
    const reactTexts = reactLinks.map(link => link.text?.toLowerCase().trim()).filter(Boolean);

    return this.calculateTextSimilarity(jspTexts, reactTexts);
  }

  /**
   * 计算可访问性分数
   */
  calculateAccessibilityScore(elements) {
    let score = 1.0;

    // 检查图片alt属性
    if (elements.images && elements.images.length > 0) {
      const missingAlt = elements.altMissingImages || 0;
      score -= (missingAlt / elements.images.length) * 0.3;
    }

    // 检查空链接
    if (elements.links && elements.links.length > 0) {
      const emptyLinks = elements.emptyLinks || 0;
      score -= (emptyLinks / elements.links.length) * 0.2;
    }

    // 检查标题结构
    if (elements.headings && elements.headings.length > 0) {
      const hasH1 = elements.headings.some(h => h.tag === 'h1');
      if (!hasH1) score -= 0.1;
    }

    return Math.max(0, score);
  }

  /**
   * 生成比较摘要
   */
  generateComparisonSummary(comparison) {
    const { score } = comparison;

    // 根据分数生成总体评价
    if (score.overall >= 0.9) {
      comparison.summary = '转换质量优秀，页面高度相似';
      comparison.level = 'excellent';
    } else if (score.overall >= 0.7) {
      comparison.summary = '转换质量良好，存在少量差异';
      comparison.level = 'good';
    } else if (score.overall >= 0.5) {
      comparison.summary = '转换质量一般，存在明显差异';
      comparison.level = 'fair';
    } else {
      comparison.summary = '转换质量较差，需要改进';
      comparison.level = 'poor';
    }

    // 收集所有相似点和差异点
    Object.values(comparison.detailedComparison).forEach(detail => {
      if (detail.similarities) {
        comparison.similarities.push(...detail.similarities);
      }
      if (detail.differences) {
        comparison.differences.push(...detail.differences);
      }
    });
  }

  /**
   * 截图对比
   */
  async compareScreenshots(jspScreenshot, reactScreenshot, identifier) {
    const comparison = {
      jspScreenshot,
      reactScreenshot,
      similarity: 0,
      differences: [],
      diffImage: null,
      analysis: {}
    };

    try {
      // 检查文件是否存在
      if (!await fs.pathExists(jspScreenshot) || !await fs.pathExists(reactScreenshot)) {
        comparison.differences.push('截图文件不存在');
        return comparison;
      }

      // 使用简单的文件大小比较作为初步分析
      const jspStats = await fs.stat(jspScreenshot);
      const reactStats = await fs.stat(reactScreenshot);

      const sizeDifference = Math.abs(jspStats.size - reactStats.size) / Math.max(jspStats.size, reactStats.size);

      comparison.analysis = {
        jspFileSize: jspStats.size,
        reactFileSize: reactStats.size,
        sizeDifference: sizeDifference,
        sizeRatio: Math.min(jspStats.size, reactStats.size) / Math.max(jspStats.size, reactStats.size)
      };

      // 基于文件大小估算相似度
      if (sizeDifference < 0.1) {
        comparison.similarity = 0.9;
        comparison.differences.push('截图文件大小相近，可能视觉相似');
      } else if (sizeDifference < 0.3) {
        comparison.similarity = 0.7;
        comparison.differences.push('截图文件大小有一定差异');
      } else {
        comparison.similarity = 0.4;
        comparison.differences.push('截图文件大小差异较大');
      }

      // 创建对比图像的路径（实际的像素对比需要额外的图像处理库）
      const diffImagePath = path.join(
        this.options.screenshotDir,
        `diff-${identifier}-${Date.now()}.png`
      );

      comparison.diffImage = diffImagePath;
      comparison.note = '详细的像素级对比需要安装图像处理库（如 pixelmatch）';

      // 如果有图像处理库，可以在这里添加真正的像素对比
      // const pixelmatch = require('pixelmatch');
      // const PNG = require('pngjs').PNG;
      // ... 实现真正的像素对比

    } catch (error) {
      comparison.differences.push(`截图对比失败: ${error.message}`);
      comparison.similarity = 0;
    }

    return comparison;
  }

  /**
   * 验证嵌入式版本
   */
  async validateEmbeddedVersion(conversionResult) {
    // 这里可以验证嵌入式 JSP 页面
    // 暂时返回基本信息
    return {
      embeddedPath: conversionResult.embeddedPath,
      validated: false,
      note: '嵌入式验证需要运行的 JSP 服务器'
    };
  }

  /**
   * 获取 JSP 路径
   */
  getJSPPath(relativePath) {
    // 移除 .jsp 扩展名，因为通常 URL 不包含扩展名
    return relativePath.replace('.jsp', '');
  }

  /**
   * 获取 React 路径
   */
  getReactPath(componentName) {
    // 根据组件名生成对应的路由
    const routeMap = {
      'Posts': '',
      'Post': 'post',
      'Create': 'create',
      'Edit': 'edit'
    };
    
    return routeMap[componentName] || componentName.toLowerCase();
  }

  /**
   * 生成验证报告
   */
  async generateValidationReport() {
    const reportPath = path.join(this.options.screenshotDir, 'validation-report.json');
    const htmlReportPath = path.join(this.options.screenshotDir, 'validation-report.html');

    // 计算统计信息
    const stats = this.calculateValidationStats();

    const report = {
      timestamp: new Date().toISOString(),
      configuration: {
        jspBaseUrl: this.options.jspBaseUrl,
        reactBaseUrl: this.options.reactBaseUrl,
        performanceMonitoring: this.options.performanceMonitoring,
        visualComparison: this.options.visualComparison,
        thresholds: {
          performance: this.options.performanceThresholds,
          visual: this.options.visualThresholds
        }
      },
      summary: {
        total: this.validationResults.length,
        successful: this.validationResults.filter(r => r.success).length,
        failed: this.validationResults.filter(r => !r.success).length,
        averageStructureSimilarity: stats.averageStructureSimilarity,
        averageLoadTime: stats.averageLoadTime,
        totalErrors: stats.totalErrors,
        totalWarnings: stats.totalWarnings
      },
      statistics: stats,
      results: this.validationResults
    };

    // 保存 JSON 报告
    await fs.writeJson(reportPath, report, { spaces: 2 });

    // 生成 HTML 报告
    await this.generateHTMLReport(report, htmlReportPath);

    console.log(chalk.blue(`📊 验证报告已生成:`));
    console.log(chalk.gray(`  JSON: ${reportPath}`));
    console.log(chalk.gray(`  HTML: ${htmlReportPath}`));

    // 打印详细摘要
    this.printValidationSummary(report);
  }

  /**
   * 计算验证统计信息
   */
  calculateValidationStats() {
    const stats = {
      averageStructureSimilarity: 0,
      averageLoadTime: { jsp: 0, react: 0 },
      totalErrors: 0,
      totalWarnings: 0,
      performanceStats: {},
      errorCategories: {},
      accessibilityIssues: 0
    };

    if (this.validationResults.length === 0) return stats;

    let totalSimilarity = 0;
    let totalJspLoadTime = 0;
    let totalReactLoadTime = 0;
    let validSimilarityCount = 0;
    let validJspLoadTimeCount = 0;
    let validReactLoadTimeCount = 0;

    this.validationResults.forEach(result => {
      // 结构相似度
      if (result.results?.comparison?.structureSimilarity) {
        totalSimilarity += result.results.comparison.structureSimilarity;
        validSimilarityCount++;
      }

      // 加载时间
      if (result.results?.jsp?.loadTime) {
        totalJspLoadTime += result.results.jsp.loadTime;
        validJspLoadTimeCount++;
      }
      if (result.results?.react?.loadTime) {
        totalReactLoadTime += result.results.react.loadTime;
        validReactLoadTimeCount++;
      }

      // 错误和警告统计
      if (result.results?.jsp?.errors) {
        stats.totalErrors += result.results.jsp.errors.length;
      }
      if (result.results?.react?.errors) {
        stats.totalErrors += result.results.react.errors.length;
      }
      if (result.results?.jsp?.warnings) {
        stats.totalWarnings += result.results.jsp.warnings.length;
      }
      if (result.results?.react?.warnings) {
        stats.totalWarnings += result.results.react.warnings.length;
      }

      // 错误分类统计
      [result.results?.jsp?.errors, result.results?.react?.errors].forEach(errors => {
        if (Array.isArray(errors)) {
          errors.forEach(error => {
            const category = error.category || error.type || 'unknown';
            stats.errorCategories[category] = (stats.errorCategories[category] || 0) + 1;
          });
        }
      });
    });

    // 计算平均值
    stats.averageStructureSimilarity = validSimilarityCount > 0 ? totalSimilarity / validSimilarityCount : 0;
    stats.averageLoadTime.jsp = validJspLoadTimeCount > 0 ? totalJspLoadTime / validJspLoadTimeCount : 0;
    stats.averageLoadTime.react = validReactLoadTimeCount > 0 ? totalReactLoadTime / validReactLoadTimeCount : 0;

    return stats;
  }

  /**
   * 打印验证摘要
   */
  printValidationSummary(report) {
    const { summary, statistics } = report;

    console.log(chalk.blue('\n📋 详细验证摘要:'));
    console.log(`总计: ${summary.total}`);
    console.log(chalk.green(`成功: ${summary.successful}`));
    console.log(chalk.red(`失败: ${summary.failed}`));

    if (summary.averageStructureSimilarity > 0) {
      const similarityColor = summary.averageStructureSimilarity > 80 ? chalk.green :
                             summary.averageStructureSimilarity > 60 ? chalk.yellow : chalk.red;
      console.log(similarityColor(`平均结构相似度: ${summary.averageStructureSimilarity.toFixed(1)}%`));
    }

    if (summary.averageLoadTime) {
      console.log(chalk.gray(`平均加载时间:`));
      console.log(chalk.gray(`  JSP: ${summary.averageLoadTime.jsp.toFixed(0)}ms`));
      console.log(chalk.gray(`  React: ${summary.averageLoadTime.react.toFixed(0)}ms`));
    }

    if (summary.totalErrors > 0) {
      console.log(chalk.red(`总错误数: ${summary.totalErrors}`));
    }

    if (summary.totalWarnings > 0) {
      console.log(chalk.yellow(`总警告数: ${summary.totalWarnings}`));
    }

    // 显示错误分类
    if (Object.keys(statistics.errorCategories).length > 0) {
      console.log(chalk.blue('\n🔍 错误分类:'));
      Object.entries(statistics.errorCategories).forEach(([category, count]) => {
        console.log(chalk.gray(`  ${category}: ${count}`));
      });
    }
  }

  /**
   * 关闭浏览器
   */
  async close() {
    if (this.browser) {
      await this.browser.close();
      console.log(chalk.gray('🔒 Puppeteer 浏览器已关闭'));
    }
  }
}

module.exports = { PuppeteerValidator };
