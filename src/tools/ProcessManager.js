const { spawn, exec } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * 进程管理器
 * 支持启动和管理不同类型的应用服务器（Maven、Gradle、npm等）
 */
class ProcessManager {
  constructor(options = {}) {
    this.options = {
      verbose: options.verbose || false,
      timeout: options.timeout || 60000, // 60秒超时
      ...options
    };
    
    this.processes = new Map();
    this.processCounter = 0;
  }

  /**
   * 检测项目类型
   */
  async detectProjectType(projectPath) {
    const detectors = [
      { file: 'pom.xml', type: 'maven', description: 'Maven 项目' },
      { file: 'build.gradle', type: 'gradle', description: 'Gradle 项目' },
      { file: 'build.gradle.kts', type: 'gradle-kotlin', description: 'Gradle <PERSON>tlin 项目' },
      { file: 'package.json', type: 'npm', description: 'Node.js 项目' },
      { file: 'yarn.lock', type: 'yarn', description: 'Yarn 项目' },
      { file: 'composer.json', type: 'composer', description: 'PHP Composer 项目' },
      { file: 'Gemfile', type: 'bundler', description: 'Ruby Bundler 项目' }
    ];

    const results = [];
    
    for (const detector of detectors) {
      const filePath = path.join(projectPath, detector.file);
      if (await fs.pathExists(filePath)) {
        results.push({
          type: detector.type,
          description: detector.description,
          configFile: detector.file,
          path: filePath
        });
      }
    }

    return results;
  }

  /**
   * 启动服务器
   */
  async startServer(projectPath, options = {}) {
    const projectTypes = await this.detectProjectType(projectPath);
    
    if (projectTypes.length === 0) {
      throw new Error(`无法检测到支持的项目类型: ${projectPath}`);
    }

    // 选择最合适的项目类型
    const projectType = this.selectBestProjectType(projectTypes, options.preferredType);
    
    console.log(chalk.blue(`🚀 启动 ${projectType.description} 服务器...`));
    console.log(chalk.gray(`项目路径: ${projectPath}`));
    console.log(chalk.gray(`配置文件: ${projectType.configFile}`));

    // 为项目设置运行环境（如果需要）
    let setupResult = null;
    if (projectType.type === 'maven') {
      setupResult = await this.setupProjectForRunning(projectType.type, projectPath, options);
    }

    const serverConfig = this.getServerConfig(projectType.type, projectPath, options);

    // 如果使用了临时配置，更新命令参数
    if (setupResult && setupResult.tempPomPath) {
      serverConfig.args = ['jetty:run', `-f`, setupResult.tempPomPath];
      serverConfig.cleanup = setupResult.cleanup;
    }

    const processId = await this.spawnProcess(serverConfig);

    return {
      processId,
      projectType: projectType.type,
      description: projectType.description,
      url: serverConfig.url,
      port: serverConfig.port,
      cleanup: setupResult?.cleanup
    };
  }

  /**
   * 选择最佳项目类型
   */
  selectBestProjectType(projectTypes, preferredType) {
    if (preferredType) {
      const preferred = projectTypes.find(p => p.type === preferredType);
      if (preferred) return preferred;
    }

    // 优先级排序
    const priority = ['maven', 'gradle', 'gradle-kotlin', 'npm', 'yarn', 'composer', 'bundler'];
    
    for (const type of priority) {
      const found = projectTypes.find(p => p.type === type);
      if (found) return found;
    }

    return projectTypes[0];
  }

  /**
   * 获取服务器配置
   */
  getServerConfig(projectType, projectPath, options = {}) {
    const configs = {
      maven: {
        command: 'mvn',
        args: this.getMavenArgs(projectPath, options),
        cwd: projectPath,
        port: options.port || 8080,
        url: `http://localhost:${options.port || 8080}`,
        env: { ...process.env, MAVEN_OPTS: '-Xmx512m' },
        readyPattern: /Started Jetty Server|Server startup in|Started Application|Started ServerConnector/i,
        errorPattern: /BUILD FAILURE|ERROR|Exception/i,
        setupRequired: true
      },
      gradle: {
        command: './gradlew',
        args: ['bootRun'],
        cwd: projectPath,
        port: options.port || 8080,
        url: `http://localhost:${options.port || 8080}`,
        env: { ...process.env },
        readyPattern: /Started .* in .* seconds|Tomcat started on port|Application startup/i,
        errorPattern: /BUILD FAILED|ERROR|Exception/i
      },
      'gradle-kotlin': {
        command: './gradlew',
        args: ['bootRun'],
        cwd: projectPath,
        port: options.port || 8080,
        url: `http://localhost:${options.port || 8080}`,
        env: { ...process.env },
        readyPattern: /Started .* in .* seconds|Tomcat started on port|Application startup/i,
        errorPattern: /BUILD FAILED|ERROR|Exception/i
      },
      npm: {
        command: 'npm',
        args: ['run', options.script || 'dev'],
        cwd: projectPath,
        port: options.port || 3000,
        url: `http://localhost:${options.port || 3000}`,
        env: { ...process.env },
        readyPattern: /Local:|ready|compiled|server running|listening/i,
        errorPattern: /ERROR|Failed to compile|Module not found/i
      },
      yarn: {
        command: 'yarn',
        args: [options.script || 'dev'],
        cwd: projectPath,
        port: options.port || 3000,
        url: `http://localhost:${options.port || 3000}`,
        env: { ...process.env },
        readyPattern: /Local:|ready|compiled|server running|listening/i,
        errorPattern: /ERROR|Failed to compile|Module not found/i
      }
    };

    const config = configs[projectType];
    if (!config) {
      throw new Error(`不支持的项目类型: ${projectType}`);
    }

    return config;
  }

  /**
   * 获取 Maven 启动参数
   */
  getMavenArgs(projectPath, options = {}) {
    // 检查项目是否已经配置了 Jetty 或 Tomcat 插件
    const pomPath = path.join(projectPath, 'pom.xml');

    try {
      const pomContent = require('fs').readFileSync(pomPath, 'utf8');

      // 检查是否已有 Jetty 插件
      if (pomContent.includes('jetty-maven-plugin')) {
        return ['jetty:run'];
      }

      // 检查是否已有 Tomcat 插件
      if (pomContent.includes('tomcat7-maven-plugin') || pomContent.includes('tomcat-maven-plugin')) {
        return ['tomcat7:run'];
      }

      // 检查是否是 Spring Boot 项目
      if (pomContent.includes('spring-boot-starter') || pomContent.includes('spring-boot-maven-plugin')) {
        return ['spring-boot:run'];
      }

    } catch (error) {
      console.log(chalk.yellow(`⚠️  无法读取 pom.xml: ${error.message}`));
    }

    // 默认情况：使用内联 Jetty 插件配置
    const port = options.port || 8080;
    return [
      'org.eclipse.jetty:jetty-maven-plugin:9.4.44.v20210927:run',
      `-Djetty.port=${port}`,
      '-Djetty.http.host=localhost'
    ];
  }

  /**
   * 为项目设置必要的运行环境
   */
  async setupProjectForRunning(projectType, projectPath, options = {}) {
    if (projectType !== 'maven') {
      return; // 只处理 Maven 项目
    }

    console.log(chalk.gray('检查 Maven 项目配置...'));

    const pomPath = path.join(projectPath, 'pom.xml');
    if (!await fs.pathExists(pomPath)) {
      throw new Error(`pom.xml 不存在: ${pomPath}`);
    }

    try {
      const pomContent = await fs.readFile(pomPath, 'utf8');

      // 检查是否需要添加插件配置
      const needsJettyPlugin = !pomContent.includes('jetty-maven-plugin') &&
                              !pomContent.includes('tomcat-maven-plugin') &&
                              !pomContent.includes('spring-boot-maven-plugin');

      if (needsJettyPlugin) {
        console.log(chalk.yellow('⚠️  项目缺少 Web 服务器插件配置'));
        console.log(chalk.gray('将使用内联 Jetty 插件启动...'));

        // 创建临时的 Maven 设置来运行 Jetty
        const tempPomPath = path.join(projectPath, 'pom-temp.xml');
        const enhancedPom = this.addJettyPluginToPom(pomContent, options.port || 8080);
        await fs.writeFile(tempPomPath, enhancedPom);

        console.log(chalk.green('✅ 临时配置已创建'));
        return { tempPomPath, cleanup: () => fs.remove(tempPomPath) };
      }

    } catch (error) {
      console.log(chalk.yellow(`⚠️  项目设置失败: ${error.message}`));
    }

    return null;
  }

  /**
   * 为 POM 添加 Jetty 插件（不修改原文件）
   */
  addJettyPluginToPom(pomContent, port = 8080) {
    // 查找 </build> 标签的位置
    const buildEndIndex = pomContent.lastIndexOf('</build>');

    if (buildEndIndex === -1) {
      // 如果没有 build 标签，在 </project> 前添加
      const projectEndIndex = pomContent.lastIndexOf('</project>');
      if (projectEndIndex === -1) {
        throw new Error('无效的 pom.xml 格式');
      }

      const jettyPlugin = `
    <build>
        <plugins>
            <plugin>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-maven-plugin</artifactId>
                <version>9.4.44.v20210927</version>
                <configuration>
                    <httpConnector>
                        <port>${port}</port>
                    </httpConnector>
                    <webApp>
                        <contextPath>/</contextPath>
                    </webApp>
                </configuration>
            </plugin>
        </plugins>
    </build>
`;

      return pomContent.substring(0, projectEndIndex) + jettyPlugin + pomContent.substring(projectEndIndex);
    } else {
      // 在现有的 build 标签中添加插件
      const jettyPlugin = `
            <plugin>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-maven-plugin</artifactId>
                <version>9.4.44.v20210927</version>
                <configuration>
                    <httpConnector>
                        <port>${port}</port>
                    </httpConnector>
                    <webApp>
                        <contextPath>/</contextPath>
                    </webApp>
                </configuration>
            </plugin>
`;

      // 查找 plugins 标签
      const pluginsStartIndex = pomContent.indexOf('<plugins>');
      if (pluginsStartIndex !== -1) {
        const pluginsEndIndex = pomContent.indexOf('</plugins>', pluginsStartIndex);
        return pomContent.substring(0, pluginsEndIndex) + jettyPlugin + pomContent.substring(pluginsEndIndex);
      } else {
        // 添加 plugins 标签
        const pluginsSection = `
        <plugins>${jettyPlugin}
        </plugins>
`;
        return pomContent.substring(0, buildEndIndex) + pluginsSection + pomContent.substring(buildEndIndex);
      }
    }
  }

  /**
   * 生成进程
   */
  async spawnProcess(config) {
    return new Promise((resolve, reject) => {
      const processId = `process_${++this.processCounter}`;
      
      console.log(chalk.gray(`执行命令: ${config.command} ${config.args.join(' ')}`));
      
      const childProcess = spawn(config.command, config.args, {
        cwd: config.cwd,
        env: config.env,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      const processInfo = {
        id: processId,
        process: childProcess,
        config,
        startTime: Date.now(),
        ready: false,
        logs: [],
        errors: []
      };

      this.processes.set(processId, processInfo);

      // 处理标准输出
      childProcess.stdout.on('data', (data) => {
        const output = data.toString();
        processInfo.logs.push({ type: 'stdout', message: output, timestamp: Date.now() });
        
        if (this.options.verbose) {
          console.log(chalk.gray(`[${processId}] ${output.trim()}`));
        }

        // 检查是否已就绪
        if (!processInfo.ready && config.readyPattern && config.readyPattern.test(output)) {
          processInfo.ready = true;
          console.log(chalk.green(`✅ 服务器已启动: ${config.url}`));
          resolve(processId);
        }
      });

      // 处理标准错误
      childProcess.stderr.on('data', (data) => {
        const output = data.toString();
        processInfo.errors.push({ type: 'stderr', message: output, timestamp: Date.now() });
        
        if (this.options.verbose) {
          console.log(chalk.red(`[${processId}] ERROR: ${output.trim()}`));
        }

        // 检查是否有错误
        if (config.errorPattern && config.errorPattern.test(output)) {
          console.log(chalk.yellow(`⚠️  检测到错误: ${output.trim()}`));
        }
      });

      // 处理进程退出
      childProcess.on('close', (code) => {
        console.log(chalk.gray(`[${processId}] 进程退出，代码: ${code}`));
        processInfo.exitCode = code;
        processInfo.endTime = Date.now();
      });

      // 处理进程错误
      childProcess.on('error', (error) => {
        console.error(chalk.red(`[${processId}] 进程错误: ${error.message}`));
        processInfo.error = error;
        reject(error);
      });

      // 超时处理
      setTimeout(() => {
        if (!processInfo.ready) {
          console.log(chalk.yellow(`⚠️  服务器启动超时，但进程仍在运行: ${config.url}`));
          resolve(processId);
        }
      }, this.options.timeout);
    });
  }

  /**
   * 停止进程
   */
  async stopProcess(processId) {
    const processInfo = this.processes.get(processId);
    if (!processInfo) {
      throw new Error(`进程不存在: ${processId}`);
    }

    console.log(chalk.blue(`🛑 停止进程: ${processId}`));
    
    return new Promise((resolve) => {
      processInfo.process.on('close', () => {
        this.processes.delete(processId);
        console.log(chalk.gray(`✅ 进程已停止: ${processId}`));
        resolve();
      });

      // 尝试优雅关闭
      processInfo.process.kill('SIGTERM');
      
      // 如果5秒后还没关闭，强制杀死
      setTimeout(() => {
        if (this.processes.has(processId)) {
          processInfo.process.kill('SIGKILL');
        }
      }, 5000);
    });
  }

  /**
   * 停止所有进程
   */
  async stopAllProcesses() {
    const processIds = Array.from(this.processes.keys());
    
    console.log(chalk.blue(`🛑 停止所有进程 (${processIds.length})`));
    
    const stopPromises = processIds.map(id => this.stopProcess(id));
    await Promise.all(stopPromises);
  }

  /**
   * 获取进程状态
   */
  getProcessStatus(processId) {
    const processInfo = this.processes.get(processId);
    if (!processInfo) {
      return null;
    }

    return {
      id: processId,
      ready: processInfo.ready,
      running: !processInfo.process.killed,
      startTime: processInfo.startTime,
      endTime: processInfo.endTime,
      exitCode: processInfo.exitCode,
      url: processInfo.config.url,
      port: processInfo.config.port,
      logs: processInfo.logs.slice(-10), // 最近10条日志
      errors: processInfo.errors.slice(-5) // 最近5条错误
    };
  }

  /**
   * 列出所有进程
   */
  listProcesses() {
    return Array.from(this.processes.keys()).map(id => this.getProcessStatus(id));
  }

  /**
   * 检查服务器健康状态
   */
  async checkServerHealth(url, timeout = 5000) {
    try {
      const response = await fetch(url, { 
        method: 'HEAD',
        timeout 
      });
      return {
        healthy: response.ok,
        status: response.status,
        statusText: response.statusText
      };
    } catch (error) {
      return {
        healthy: false,
        error: error.message
      };
    }
  }

  /**
   * 等待服务器就绪
   */
  async waitForServer(url, maxAttempts = 30, interval = 2000) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      const health = await this.checkServerHealth(url);
      
      if (health.healthy) {
        console.log(chalk.green(`✅ 服务器就绪: ${url}`));
        return true;
      }

      if (attempt < maxAttempts) {
        console.log(chalk.gray(`⏳ 等待服务器就绪 (${attempt}/${maxAttempts})...`));
        await new Promise(resolve => setTimeout(resolve, interval));
      }
    }

    console.log(chalk.red(`❌ 服务器启动超时: ${url}`));
    return false;
  }
}

module.exports = { ProcessManager };
