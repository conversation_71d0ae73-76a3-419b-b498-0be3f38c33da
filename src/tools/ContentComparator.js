const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * 内容对比器
 * 基于 DOM 结构和文本内容进行智能对比，而不是简单的截图对比
 */
class ContentComparator {
  constructor(options = {}) {
    this.options = {
      verbose: options.verbose || false,
      aiApiKey: options.aiApiKey || process.env.OPENAI_API_KEY,
      aiModel: options.aiModel || 'gpt-3.5-turbo',
      semanticThreshold: options.semanticThreshold || 0.8,
      structureWeight: options.structureWeight || 0.4,
      contentWeight: options.contentWeight || 0.4,
      semanticWeight: options.semanticWeight || 0.2,
      ...options
    };
  }

  /**
   * 提取页面内容结构
   */
  async extractPageContent(page) {
    return await page.evaluate(() => {
      // 移除脚本和样式标签
      const scripts = document.querySelectorAll('script, style, noscript');
      scripts.forEach(el => el.remove());

      const content = {
        title: document.title,
        meta: {
          description: document.querySelector('meta[name="description"]')?.content || '',
          keywords: document.querySelector('meta[name="keywords"]')?.content || ''
        },
        structure: {
          headings: [],
          paragraphs: [],
          lists: [],
          tables: [],
          forms: [],
          links: [],
          images: []
        },
        textContent: '',
        semanticBlocks: []
      };

      // 提取标题结构
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      headings.forEach(heading => {
        content.structure.headings.push({
          level: parseInt(heading.tagName.charAt(1)),
          text: heading.textContent.trim(),
          id: heading.id || '',
          classes: Array.from(heading.classList)
        });
      });

      // 提取段落
      const paragraphs = document.querySelectorAll('p');
      paragraphs.forEach(p => {
        const text = p.textContent.trim();
        if (text.length > 10) { // 过滤掉太短的段落
          content.structure.paragraphs.push({
            text: text,
            length: text.length,
            classes: Array.from(p.classList)
          });
        }
      });

      // 提取列表
      const lists = document.querySelectorAll('ul, ol');
      lists.forEach(list => {
        const items = Array.from(list.querySelectorAll('li')).map(li => li.textContent.trim());
        content.structure.lists.push({
          type: list.tagName.toLowerCase(),
          items: items,
          itemCount: items.length
        });
      });

      // 提取表格
      const tables = document.querySelectorAll('table');
      tables.forEach(table => {
        const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim());
        const rows = Array.from(table.querySelectorAll('tr')).map(tr => {
          return Array.from(tr.querySelectorAll('td')).map(td => td.textContent.trim());
        }).filter(row => row.length > 0);
        
        content.structure.tables.push({
          headers: headers,
          rowCount: rows.length,
          columnCount: headers.length || (rows[0]?.length || 0),
          sampleData: rows.slice(0, 3) // 只取前3行作为样本
        });
      });

      // 提取表单
      const forms = document.querySelectorAll('form');
      forms.forEach(form => {
        const inputs = Array.from(form.querySelectorAll('input, select, textarea')).map(input => ({
          type: input.type || input.tagName.toLowerCase(),
          name: input.name || '',
          placeholder: input.placeholder || '',
          required: input.required
        }));
        
        content.structure.forms.push({
          action: form.action || '',
          method: form.method || 'get',
          inputs: inputs,
          inputCount: inputs.length
        });
      });

      // 提取链接
      const links = document.querySelectorAll('a[href]');
      links.forEach(link => {
        const text = link.textContent.trim();
        if (text.length > 0) {
          content.structure.links.push({
            text: text,
            href: link.href,
            isExternal: link.href.startsWith('http') && !link.href.includes(window.location.hostname)
          });
        }
      });

      // 提取图片
      const images = document.querySelectorAll('img');
      images.forEach(img => {
        content.structure.images.push({
          src: img.src,
          alt: img.alt || '',
          width: img.width || 0,
          height: img.height || 0
        });
      });

      // 提取主要文本内容
      const bodyText = document.body.textContent || '';
      content.textContent = bodyText.replace(/\s+/g, ' ').trim();

      // 提取语义块（基于常见的语义标签）
      const semanticTags = ['header', 'nav', 'main', 'article', 'section', 'aside', 'footer'];
      semanticTags.forEach(tag => {
        const elements = document.querySelectorAll(tag);
        elements.forEach((el, index) => {
          const text = el.textContent.trim();
          if (text.length > 20) {
            content.semanticBlocks.push({
              tag: tag,
              index: index,
              text: text.substring(0, 500), // 限制长度
              wordCount: text.split(/\s+/).length
            });
          }
        });
      });

      return content;
    });
  }

  /**
   * 对比两个页面内容
   */
  async comparePageContents(jspContent, reactContent) {
    const comparison = {
      structureScore: 0,
      contentScore: 0,
      semanticScore: 0,
      overallScore: 0,
      details: {
        structure: {},
        content: {},
        semantic: {}
      },
      recommendations: []
    };

    // 1. 结构对比
    comparison.details.structure = this.compareStructure(jspContent.structure, reactContent.structure);
    comparison.structureScore = comparison.details.structure.score;

    // 2. 内容对比
    comparison.details.content = this.compareContent(jspContent, reactContent);
    comparison.contentScore = comparison.details.content.score;

    // 3. 语义对比（如果配置了 AI）
    if (this.options.aiApiKey) {
      comparison.details.semantic = await this.compareSemantics(jspContent, reactContent);
      comparison.semanticScore = comparison.details.semantic.score;
    } else {
      comparison.semanticScore = this.calculateBasicSemanticScore(jspContent, reactContent);
    }

    // 计算总体分数
    comparison.overallScore = (
      comparison.structureScore * this.options.structureWeight +
      comparison.contentScore * this.options.contentWeight +
      comparison.semanticScore * this.options.semanticWeight
    );

    // 生成建议
    comparison.recommendations = this.generateRecommendations(comparison);

    return comparison;
  }

  /**
   * 对比页面结构
   */
  compareStructure(jspStructure, reactStructure) {
    const comparison = {
      score: 0,
      matches: [],
      differences: [],
      details: {}
    };

    let totalScore = 0;
    let maxScore = 0;

    // 对比标题结构
    const headingComparison = this.compareHeadings(jspStructure.headings, reactStructure.headings);
    comparison.details.headings = headingComparison;
    totalScore += headingComparison.score * 30; // 权重30%
    maxScore += 30;

    // 对比段落
    const paragraphComparison = this.compareParagraphs(jspStructure.paragraphs, reactStructure.paragraphs);
    comparison.details.paragraphs = paragraphComparison;
    totalScore += paragraphComparison.score * 25; // 权重25%
    maxScore += 25;

    // 对比列表
    const listComparison = this.compareLists(jspStructure.lists, reactStructure.lists);
    comparison.details.lists = listComparison;
    totalScore += listComparison.score * 15; // 权重15%
    maxScore += 15;

    // 对比表格
    const tableComparison = this.compareTables(jspStructure.tables, reactStructure.tables);
    comparison.details.tables = tableComparison;
    totalScore += tableComparison.score * 15; // 权重15%
    maxScore += 15;

    // 对比表单
    const formComparison = this.compareForms(jspStructure.forms, reactStructure.forms);
    comparison.details.forms = formComparison;
    totalScore += formComparison.score * 15; // 权重15%
    maxScore += 15;

    comparison.score = maxScore > 0 ? totalScore / maxScore : 0;

    // 收集匹配和差异
    [headingComparison, paragraphComparison, listComparison, tableComparison, formComparison].forEach(comp => {
      comparison.matches.push(...(comp.matches || []));
      comparison.differences.push(...(comp.differences || []));
    });

    return comparison;
  }

  /**
   * 对比标题结构
   */
  compareHeadings(jspHeadings, reactHeadings) {
    const comparison = {
      score: 0,
      matches: [],
      differences: [],
      jspCount: jspHeadings.length,
      reactCount: reactHeadings.length
    };

    if (jspHeadings.length === 0 && reactHeadings.length === 0) {
      comparison.score = 1;
      return comparison;
    }

    // 提取标题文本进行对比
    const jspTexts = jspHeadings.map(h => h.text.toLowerCase().trim());
    const reactTexts = reactHeadings.map(h => h.text.toLowerCase().trim());

    // 计算文本匹配度
    const textSimilarity = this.calculateTextArraySimilarity(jspTexts, reactTexts);
    
    // 计算层级结构匹配度
    const jspLevels = jspHeadings.map(h => h.level);
    const reactLevels = reactHeadings.map(h => h.level);
    const levelSimilarity = this.calculateArraySimilarity(jspLevels, reactLevels);

    comparison.score = (textSimilarity * 0.7 + levelSimilarity * 0.3);

    if (textSimilarity > 0.8) {
      comparison.matches.push('标题内容高度匹配');
    } else {
      comparison.differences.push(`标题内容匹配度较低: ${(textSimilarity * 100).toFixed(1)}%`);
    }

    if (Math.abs(jspHeadings.length - reactHeadings.length) <= 1) {
      comparison.matches.push('标题数量基本一致');
    } else {
      comparison.differences.push(`标题数量差异: JSP=${jspHeadings.length}, React=${reactHeadings.length}`);
    }

    return comparison;
  }

  /**
   * 对比段落内容
   */
  compareParagraphs(jspParagraphs, reactParagraphs) {
    const comparison = {
      score: 0,
      matches: [],
      differences: [],
      jspCount: jspParagraphs.length,
      reactCount: reactParagraphs.length
    };

    if (jspParagraphs.length === 0 && reactParagraphs.length === 0) {
      comparison.score = 1;
      return comparison;
    }

    const jspTexts = jspParagraphs.map(p => p.text.toLowerCase().trim());
    const reactTexts = reactParagraphs.map(p => p.text.toLowerCase().trim());

    const textSimilarity = this.calculateTextArraySimilarity(jspTexts, reactTexts);
    comparison.score = textSimilarity;

    if (textSimilarity > 0.8) {
      comparison.matches.push('段落内容高度匹配');
    } else {
      comparison.differences.push(`段落内容匹配度: ${(textSimilarity * 100).toFixed(1)}%`);
    }

    return comparison;
  }

  /**
   * 对比列表
   */
  compareLists(jspLists, reactLists) {
    const comparison = {
      score: 0,
      matches: [],
      differences: [],
      jspCount: jspLists ? jspLists.length : 0,
      reactCount: reactLists ? reactLists.length : 0
    };

    // 处理 undefined 或 null 的情况
    const jspListsArray = jspLists || [];
    const reactListsArray = reactLists || [];

    if (jspListsArray.length === 0 && reactListsArray.length === 0) {
      comparison.score = 1;
      return comparison;
    }

    if (jspListsArray.length === reactListsArray.length) {
      comparison.matches.push('列表数量一致');

      let totalSimilarity = 0;
      for (let i = 0; i < jspListsArray.length; i++) {
        const jspItems = (jspListsArray[i].items || []).map(item => item.toLowerCase().trim());
        const reactItems = (reactListsArray[i].items || []).map(item => item.toLowerCase().trim());
        totalSimilarity += this.calculateTextArraySimilarity(jspItems, reactItems);
      }
      comparison.score = jspListsArray.length > 0 ? totalSimilarity / jspListsArray.length : 1;
    } else {
      comparison.differences.push(`列表数量不同: JSP=${jspListsArray.length}, React=${reactListsArray.length}`);
      comparison.score = 0.5;
    }

    return comparison;
  }

  /**
   * 对比表格
   */
  compareTables(jspTables, reactTables) {
    const comparison = {
      score: 0,
      matches: [],
      differences: [],
      jspCount: jspTables ? jspTables.length : 0,
      reactCount: reactTables ? reactTables.length : 0
    };

    // 处理 undefined 或 null 的情况
    const jspTablesArray = jspTables || [];
    const reactTablesArray = reactTables || [];

    if (jspTablesArray.length === 0 && reactTablesArray.length === 0) {
      comparison.score = 1;
      return comparison;
    }

    if (jspTablesArray.length === reactTablesArray.length) {
      comparison.matches.push('表格数量一致');

      let totalSimilarity = 0;
      for (let i = 0; i < jspTablesArray.length; i++) {
        const jspHeaders = (jspTablesArray[i].headers || []).map(h => h.toLowerCase().trim());
        const reactHeaders = (reactTablesArray[i].headers || []).map(h => h.toLowerCase().trim());
        const headerSimilarity = this.calculateTextArraySimilarity(jspHeaders, reactHeaders);

        const structureSimilarity = jspTablesArray[i].rowCount === reactTablesArray[i].rowCount &&
                                   jspTablesArray[i].columnCount === reactTablesArray[i].columnCount ? 1 : 0.5;

        totalSimilarity += (headerSimilarity * 0.6 + structureSimilarity * 0.4);
      }
      comparison.score = jspTablesArray.length > 0 ? totalSimilarity / jspTablesArray.length : 1;
    } else {
      comparison.differences.push(`表格数量不同: JSP=${jspTablesArray.length}, React=${reactTablesArray.length}`);
      comparison.score = 0.3;
    }

    return comparison;
  }

  /**
   * 对比表单
   */
  compareForms(jspForms, reactForms) {
    const comparison = {
      score: 0,
      matches: [],
      differences: [],
      jspCount: jspForms ? jspForms.length : 0,
      reactCount: reactForms ? reactForms.length : 0
    };

    // 处理 undefined 或 null 的情况
    const jspFormsArray = jspForms || [];
    const reactFormsArray = reactForms || [];

    if (jspFormsArray.length === 0 && reactFormsArray.length === 0) {
      comparison.score = 1;
      return comparison;
    }

    if (jspFormsArray.length === reactFormsArray.length) {
      comparison.matches.push('表单数量一致');

      let totalSimilarity = 0;
      for (let i = 0; i < jspFormsArray.length; i++) {
        const jspInputTypes = (jspFormsArray[i].inputs || []).map(input => input.type);
        const reactInputTypes = (reactFormsArray[i].inputs || []).map(input => input.type);
        const inputSimilarity = this.calculateArraySimilarity(jspInputTypes, reactInputTypes);
        totalSimilarity += inputSimilarity;
      }
      comparison.score = jspFormsArray.length > 0 ? totalSimilarity / jspFormsArray.length : 1;
    } else {
      comparison.differences.push(`表单数量不同: JSP=${jspFormsArray.length}, React=${reactFormsArray.length}`);
      comparison.score = 0.3;
    }

    return comparison;
  }

  /**
   * 计算文本数组相似度
   */
  calculateTextArraySimilarity(arr1, arr2) {
    if (arr1.length === 0 && arr2.length === 0) return 1;
    if (arr1.length === 0 || arr2.length === 0) return 0;

    const set1 = new Set(arr1);
    const set2 = new Set(arr2);
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);

    return intersection.size / union.size;
  }

  /**
   * 计算数组相似度
   */
  calculateArraySimilarity(arr1, arr2) {
    if (arr1.length === 0 && arr2.length === 0) return 1;
    if (arr1.length === 0 || arr2.length === 0) return 0;

    const maxLength = Math.max(arr1.length, arr2.length);
    let matches = 0;

    for (let i = 0; i < maxLength; i++) {
      if (arr1[i] === arr2[i]) {
        matches++;
      }
    }

    return matches / maxLength;
  }

  /**
   * 对比页面内容
   */
  compareContent(jspContent, reactContent) {
    const comparison = {
      score: 0,
      matches: [],
      differences: [],
      details: {}
    };

    // 对比标题
    const titleSimilarity = this.calculateStringSimilarity(
      jspContent.title.toLowerCase(),
      reactContent.title.toLowerCase()
    );
    comparison.details.title = { similarity: titleSimilarity };

    // 对比主要文本内容
    const textSimilarity = this.calculateStringSimilarity(
      jspContent.textContent.toLowerCase(),
      reactContent.textContent.toLowerCase()
    );
    comparison.details.textContent = { similarity: textSimilarity };

    // 对比语义块
    const semanticSimilarity = this.compareSemanticBlocks(
      jspContent.semanticBlocks,
      reactContent.semanticBlocks
    );
    comparison.details.semanticBlocks = semanticSimilarity;

    // 计算总体内容分数
    comparison.score = (titleSimilarity * 0.2 + textSimilarity * 0.5 + semanticSimilarity.score * 0.3);

    // 生成匹配和差异描述
    if (titleSimilarity > 0.8) {
      comparison.matches.push('页面标题高度匹配');
    } else {
      comparison.differences.push(`页面标题相似度: ${(titleSimilarity * 100).toFixed(1)}%`);
    }

    if (textSimilarity > 0.7) {
      comparison.matches.push('文本内容高度匹配');
    } else {
      comparison.differences.push(`文本内容相似度: ${(textSimilarity * 100).toFixed(1)}%`);
    }

    return comparison;
  }

  /**
   * 对比语义块
   */
  compareSemanticBlocks(jspBlocks, reactBlocks) {
    const comparison = {
      score: 0,
      matches: [],
      differences: [],
      blockComparisons: []
    };

    if (jspBlocks.length === 0 && reactBlocks.length === 0) {
      comparison.score = 1;
      return comparison;
    }

    // 按标签类型分组
    const jspByTag = this.groupBlocksByTag(jspBlocks);
    const reactByTag = this.groupBlocksByTag(reactBlocks);

    const allTags = new Set([...Object.keys(jspByTag), ...Object.keys(reactByTag)]);
    let totalSimilarity = 0;
    let tagCount = 0;

    allTags.forEach(tag => {
      const jspTagBlocks = jspByTag[tag] || [];
      const reactTagBlocks = reactByTag[tag] || [];

      if (jspTagBlocks.length > 0 || reactTagBlocks.length > 0) {
        const tagSimilarity = this.compareTagBlocks(jspTagBlocks, reactTagBlocks);
        comparison.blockComparisons.push({
          tag,
          similarity: tagSimilarity,
          jspCount: jspTagBlocks.length,
          reactCount: reactTagBlocks.length
        });

        totalSimilarity += tagSimilarity;
        tagCount++;
      }
    });

    comparison.score = tagCount > 0 ? totalSimilarity / tagCount : 0;
    return comparison;
  }

  /**
   * 按标签分组语义块
   */
  groupBlocksByTag(blocks) {
    const grouped = {};
    blocks.forEach(block => {
      if (!grouped[block.tag]) {
        grouped[block.tag] = [];
      }
      grouped[block.tag].push(block);
    });
    return grouped;
  }

  /**
   * 对比同类型标签的块
   */
  compareTagBlocks(jspBlocks, reactBlocks) {
    if (jspBlocks.length === 0 && reactBlocks.length === 0) return 1;
    if (jspBlocks.length === 0 || reactBlocks.length === 0) return 0;

    const jspTexts = jspBlocks.map(block => block.text.toLowerCase());
    const reactTexts = reactBlocks.map(block => block.text.toLowerCase());

    return this.calculateTextArraySimilarity(jspTexts, reactTexts);
  }

  /**
   * 计算字符串相似度（使用简单的 Jaccard 相似度）
   */
  calculateStringSimilarity(str1, str2) {
    if (str1 === str2) return 1;
    if (str1.length === 0 && str2.length === 0) return 1;
    if (str1.length === 0 || str2.length === 0) return 0;

    // 分词
    const words1 = str1.split(/\s+/).filter(word => word.length > 2);
    const words2 = str2.split(/\s+/).filter(word => word.length > 2);

    return this.calculateTextArraySimilarity(words1, words2);
  }

  /**
   * 使用 AI 进行语义对比
   */
  async compareSemantics(jspContent, reactContent) {
    const comparison = {
      score: 0,
      analysis: '',
      matches: [],
      differences: [],
      aiResponse: null
    };

    try {
      const prompt = this.buildSemanticComparisonPrompt(jspContent, reactContent);
      const aiResponse = await this.callAI(prompt);

      comparison.aiResponse = aiResponse;
      comparison.analysis = aiResponse.analysis || '';
      comparison.score = aiResponse.similarity_score || 0;
      comparison.matches = aiResponse.matches || [];
      comparison.differences = aiResponse.differences || [];

    } catch (error) {
      console.log(chalk.yellow(`⚠️  AI 语义分析失败: ${error.message}`));
      comparison.score = this.calculateBasicSemanticScore(jspContent, reactContent);
      comparison.analysis = '使用基础算法进行语义分析';
    }

    return comparison;
  }

  /**
   * 构建语义对比提示词
   */
  buildSemanticComparisonPrompt(jspContent, reactContent) {
    return `
请分析以下两个页面的语义相似度，这是一个 JSP 到 React 的转换验证：

JSP 页面内容：
标题: ${jspContent.title}
主要文本: ${jspContent.textContent.substring(0, 1000)}...
语义块: ${JSON.stringify(jspContent.semanticBlocks.slice(0, 5), null, 2)}

React 页面内容：
标题: ${reactContent.title}
主要文本: ${reactContent.textContent.substring(0, 1000)}...
语义块: ${JSON.stringify(reactContent.semanticBlocks.slice(0, 5), null, 2)}

请以 JSON 格式返回分析结果：
{
  "similarity_score": 0.85,
  "analysis": "详细分析说明",
  "matches": ["匹配点1", "匹配点2"],
  "differences": ["差异点1", "差异点2"],
  "recommendations": ["建议1", "建议2"]
}

评分标准：
- 1.0: 语义完全一致
- 0.8-0.9: 语义高度一致，细节有差异
- 0.6-0.7: 语义基本一致，有明显差异
- 0.4-0.5: 语义部分一致
- 0.0-0.3: 语义差异很大
`;
  }

  /**
   * 调用 AI API
   */
  async callAI(prompt) {
    // 这里可以集成不同的 AI 服务
    // 示例使用 OpenAI API
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.options.aiApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: this.options.aiModel,
        messages: [
          {
            role: 'system',
            content: '你是一个专业的网页内容分析师，专门分析 JSP 到 React 转换的质量。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      throw new Error(`AI API 调用失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices[0]?.message?.content;

    try {
      return JSON.parse(content);
    } catch (error) {
      throw new Error('AI 返回的不是有效的 JSON 格式');
    }
  }

  /**
   * 计算基础语义分数（不使用 AI）
   */
  calculateBasicSemanticScore(jspContent, reactContent) {
    const titleSim = this.calculateStringSimilarity(jspContent.title, reactContent.title);
    const textSim = this.calculateStringSimilarity(jspContent.textContent, reactContent.textContent);
    const blockSim = this.compareSemanticBlocks(jspContent.semanticBlocks, reactContent.semanticBlocks).score;

    return (titleSim * 0.3 + textSim * 0.4 + blockSim * 0.3);
  }

  /**
   * 生成改进建议
   */
  generateRecommendations(comparison) {
    const recommendations = [];

    if (comparison.structureScore < 0.7) {
      recommendations.push('建议检查页面结构转换，确保标题、段落、列表等元素正确迁移');
    }

    if (comparison.contentScore < 0.7) {
      recommendations.push('建议检查文本内容是否完整迁移，注意动态内容的处理');
    }

    if (comparison.semanticScore < 0.7) {
      recommendations.push('建议检查页面语义结构，确保用户体验一致');
    }

    if (comparison.overallScore < 0.6) {
      recommendations.push('转换质量较低，建议重新检查转换逻辑和模板');
    }

    if (recommendations.length === 0) {
      recommendations.push('转换质量良好，可以考虑进行更细致的样式和交互优化');
    }

    return recommendations;
  }
}

module.exports = { ContentComparator };
