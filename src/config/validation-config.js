const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * 验证配置管理器
 * 管理验证规则、阈值和自定义配置
 */
class ValidationConfig {
  constructor(configPath = null) {
    this.configPath = configPath;
    this.config = this.getDefaultConfig();
    
    if (configPath) {
      this.loadConfig(configPath);
    }
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig() {
    return {
      // 基本设置
      general: {
        timeout: 30000,
        headless: true,
        verbose: false,
        screenshotDir: './validation-results',
        maxConcurrentPages: 3
      },

      // 服务器配置
      servers: {
        jsp: {
          baseUrl: 'http://localhost:8080',
          healthCheckPath: '/',
          startupTimeout: 60000,
          readyPattern: /Started|Server startup|Tomcat started/i
        },
        react: {
          baseUrl: 'http://localhost:3000',
          healthCheckPath: '/',
          startupTimeout: 60000,
          readyPattern: /Local:|ready|compiled|listening/i
        }
      },

      // 验证阈值
      thresholds: {
        // 结构相似度阈值
        structure: {
          excellent: 0.9,   // 优秀
          good: 0.7,        // 良好
          acceptable: 0.5,  // 可接受
          poor: 0.3         // 较差
        },

        // 内容相似度阈值
        content: {
          excellent: 0.9,
          good: 0.8,
          acceptable: 0.6,
          poor: 0.4
        },

        // 语义相似度阈值
        semantic: {
          excellent: 0.9,
          good: 0.8,
          acceptable: 0.7,
          poor: 0.5
        },

        // 性能阈值
        performance: {
          loadTime: {
            excellent: 2000,  // 2秒
            good: 5000,       // 5秒
            acceptable: 10000, // 10秒
            poor: 15000       // 15秒
          },
          firstContentfulPaint: {
            excellent: 1500,
            good: 3000,
            acceptable: 5000,
            poor: 8000
          }
        },

        // 视觉相似度阈值（像素差异百分比）
        visual: {
          excellent: 0.05,  // 5% 差异
          good: 0.15,       // 15% 差异
          acceptable: 0.3,  // 30% 差异
          poor: 0.5         // 50% 差异
        }
      },

      // 权重配置
      weights: {
        structure: 0.3,    // 结构权重
        content: 0.4,      // 内容权重
        semantic: 0.2,     // 语义权重
        performance: 0.1   // 性能权重
      },

      // 验证规则
      rules: {
        // 必须验证的元素
        required: {
          title: true,           // 页面标题
          headings: true,        // 标题结构
          forms: true,           // 表单
          navigation: true,      // 导航
          mainContent: true      // 主要内容
        },

        // 可选验证的元素
        optional: {
          images: true,          // 图片
          links: true,           // 链接
          tables: true,          // 表格
          lists: true,           // 列表
          footer: true           // 页脚
        },

        // 忽略的元素
        ignore: {
          scripts: true,         // 脚本标签
          styles: true,          // 样式标签
          comments: true,        // 注释
          whitespace: true,      // 空白字符
          timestamps: true,      // 时间戳
          sessionIds: true       // 会话ID
        },

        // 错误处理规则
        errorHandling: {
          continueOnError: true,     // 遇到错误时继续
          maxErrors: 10,             // 最大错误数
          retryAttempts: 3,          // 重试次数
          retryDelay: 2000           // 重试延迟（毫秒）
        }
      },

      // AI 配置
      ai: {
        enabled: false,
        provider: 'openai',        // openai, anthropic, local
        model: 'gpt-3.5-turbo',
        apiKey: null,
        maxTokens: 1000,
        temperature: 0.3,
        timeout: 30000,
        
        // 提示词模板
        prompts: {
          semanticComparison: `
分析以下两个页面的语义相似度，这是一个 JSP 到 React 的转换验证：

JSP 页面：
{jsp_content}

React 页面：
{react_content}

请返回 JSON 格式的分析结果：
{
  "similarity_score": 0.85,
  "analysis": "详细分析",
  "matches": ["匹配点"],
  "differences": ["差异点"],
  "recommendations": ["建议"]
}
          `
        }
      },

      // 报告配置
      reporting: {
        formats: ['html', 'json'],     // 报告格式
        includeScreenshots: true,      // 包含截图
        includePerformance: true,      // 包含性能数据
        includeErrors: true,           // 包含错误信息
        includeRecommendations: true,  // 包含改进建议
        
        // HTML 报告配置
        html: {
          theme: 'default',            // 主题
          showDetails: true,           // 显示详细信息
          interactive: true,           // 交互式报告
          embedImages: false           // 嵌入图片
        }
      },

      // 自定义验证器
      customValidators: [
        // 示例：自定义表单验证器
        {
          name: 'formValidation',
          enabled: true,
          config: {
            checkRequired: true,
            checkValidation: true,
            checkSubmission: true
          }
        },
        
        // 示例：自定义导航验证器
        {
          name: 'navigationValidation',
          enabled: true,
          config: {
            checkMenuItems: true,
            checkActiveStates: true,
            checkResponsive: true
          }
        }
      ]
    };
  }

  /**
   * 加载配置文件
   */
  async loadConfig(configPath) {
    try {
      if (await fs.pathExists(configPath)) {
        const userConfig = await fs.readJson(configPath);
        this.config = this.mergeConfig(this.config, userConfig);
        console.log(chalk.green(`✅ 已加载配置文件: ${configPath}`));
      } else {
        console.log(chalk.yellow(`⚠️  配置文件不存在: ${configPath}`));
      }
    } catch (error) {
      console.error(chalk.red(`❌ 加载配置文件失败: ${error.message}`));
    }
  }

  /**
   * 保存配置文件
   */
  async saveConfig(configPath = null) {
    const savePath = configPath || this.configPath || './validation-config.json';
    
    try {
      await fs.ensureDir(path.dirname(savePath));
      await fs.writeJson(savePath, this.config, { spaces: 2 });
      console.log(chalk.green(`✅ 配置已保存到: ${savePath}`));
    } catch (error) {
      console.error(chalk.red(`❌ 保存配置失败: ${error.message}`));
    }
  }

  /**
   * 深度合并配置
   */
  mergeConfig(defaultConfig, userConfig) {
    const merged = { ...defaultConfig };
    
    for (const key in userConfig) {
      if (userConfig[key] && typeof userConfig[key] === 'object' && !Array.isArray(userConfig[key])) {
        merged[key] = this.mergeConfig(merged[key] || {}, userConfig[key]);
      } else {
        merged[key] = userConfig[key];
      }
    }
    
    return merged;
  }

  /**
   * 获取配置值
   */
  get(path, defaultValue = null) {
    const keys = path.split('.');
    let current = this.config;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return defaultValue;
      }
    }
    
    return current;
  }

  /**
   * 设置配置值
   */
  set(path, value) {
    const keys = path.split('.');
    let current = this.config;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }
    
    current[keys[keys.length - 1]] = value;
  }

  /**
   * 验证配置
   */
  validate() {
    const errors = [];
    
    // 验证必需的配置
    if (!this.get('servers.jsp.baseUrl')) {
      errors.push('JSP 服务器 URL 未配置');
    }
    
    if (!this.get('servers.react.baseUrl')) {
      errors.push('React 服务器 URL 未配置');
    }
    
    // 验证阈值范围
    const thresholds = this.get('thresholds');
    for (const category in thresholds) {
      if (typeof thresholds[category] === 'object') {
        for (const level in thresholds[category]) {
          const value = thresholds[category][level];
          if (typeof value === 'number' && (value < 0 || value > 1)) {
            errors.push(`阈值 ${category}.${level} 应该在 0-1 之间`);
          }
        }
      }
    }
    
    // 验证权重总和
    const weights = this.get('weights');
    const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
    if (Math.abs(totalWeight - 1) > 0.01) {
      errors.push(`权重总和应该为 1，当前为 ${totalWeight}`);
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 生成示例配置文件
   */
  async generateExampleConfig(outputPath = './validation-config.example.json') {
    const exampleConfig = {
      general: {
        verbose: true,
        screenshotDir: './my-validation-results'
      },
      servers: {
        jsp: {
          baseUrl: 'http://localhost:8080'
        },
        react: {
          baseUrl: 'http://localhost:3000'
        }
      },
      thresholds: {
        structure: {
          acceptable: 0.6
        },
        content: {
          acceptable: 0.7
        }
      },
      ai: {
        enabled: true,
        apiKey: 'your-api-key-here'
      }
    };
    
    await fs.writeJson(outputPath, exampleConfig, { spaces: 2 });
    console.log(chalk.green(`✅ 示例配置已生成: ${outputPath}`));
  }
}

module.exports = { ValidationConfig };
