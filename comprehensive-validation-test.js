#!/usr/bin/env node

/**
 * 综合验证测试脚本
 * 测试 JSP 和 React 页面的实际对比功能
 */

const puppeteer = require('puppeteer');
const { ContentComparator } = require('./src/tools/ContentComparator');
const chalk = require('chalk');
const fs = require('fs-extra');
const path = require('path');

class ComprehensiveValidationTest {
  constructor() {
    this.browser = null;
    this.contentComparator = new ContentComparator({ verbose: true });
  }

  async run() {
    console.log(chalk.blue('🔬 综合验证测试'));
    console.log(chalk.gray('对比 JSP 和 React 页面内容'));
    console.log(chalk.gray('=' .repeat(60)));

    try {
      // 1. 启动浏览器
      await this.initBrowser();

      // 2. 创建模拟的 JSP 和 React 页面
      await this.createTestPages();

      // 3. 提取两个页面的内容
      const jspContent = await this.extractJSPContent();
      const reactContent = await this.extractReactContent();

      // 4. 进行智能对比
      const comparison = await this.compareContents(jspContent, reactContent);

      // 5. 生成详细报告
      await this.generateDetailedReport(jspContent, reactContent, comparison);

      console.log(chalk.green('\n🎉 综合验证测试完成！'));

    } catch (error) {
      console.error(chalk.red('\n❌ 测试失败:'), error.message);
      console.error(chalk.gray(error.stack));
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 初始化浏览器
   */
  async initBrowser() {
    console.log(chalk.blue('\n📋 步骤 1: 初始化浏览器'));
    
    this.browser = await puppeteer.launch({
      headless: "new",
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    console.log(chalk.green('✅ Puppeteer 浏览器已启动'));
  }

  /**
   * 创建测试页面
   */
  async createTestPages() {
    console.log(chalk.blue('\n📋 步骤 2: 创建测试页面'));

    // 创建模拟的 JSP 页面内容
    this.jspPageContent = `
<!DOCTYPE html>
<html>
<head>
    <title>JSP 博客系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .post { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .post h3 { color: #333; margin-top: 0; }
        .post-date { color: #666; font-size: 0.9em; }
        .continue-link { color: #007bff; text-decoration: none; }
        .create-form { margin-top: 30px; padding: 20px; background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>我的博客</h1>
        
        <div class="post">
            <h3>Spring Boot 入门指南</h3>
            <p class="post-date">2024-01-15</p>
            <p>这是一篇关于 Spring Boot 框架的入门指南。Spring Boot 是一个基于 Spring 框架的快速开发工具...</p>
            <a href="/posts/1" class="continue-link">继续阅读</a>
        </div>
        
        <div class="post">
            <h3>React 组件最佳实践</h3>
            <p class="post-date">2024-01-16</p>
            <p>在这篇文章中，我们将探讨 React 组件开发的最佳实践，包括组件设计原则、状态管理等...</p>
            <a href="/posts/2" class="continue-link">继续阅读</a>
        </div>
        
        <div class="post">
            <h3>数据库设计原则</h3>
            <p class="post-date">2024-01-17</p>
            <p>良好的数据库设计是应用程序成功的关键。本文将介绍数据库设计的基本原则和最佳实践...</p>
            <a href="/posts/3" class="continue-link">继续阅读</a>
        </div>
        
        <div class="create-form">
            <h2>创建新文章</h2>
            <form action="/posts" method="post">
                <p>
                    <label>标题:</label><br>
                    <input type="text" name="title" style="width: 100%; padding: 8px;">
                </p>
                <p>
                    <label>内容:</label><br>
                    <textarea name="content" rows="5" style="width: 100%; padding: 8px;"></textarea>
                </p>
                <p>
                    <input type="submit" value="发布文章" style="padding: 10px 20px; background: #007bff; color: white; border: none;">
                </p>
            </form>
        </div>
    </div>
</body>
</html>`;

    // 创建模拟的 React 页面内容
    this.reactPageContent = `
<!DOCTYPE html>
<html>
<head>
    <title>React 博客系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .post { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .post h3 { color: #333; margin-top: 0; }
        .post-date { color: #666; font-size: 0.9em; }
        .continue-link { color: #007bff; text-decoration: none; }
        .create-form { margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>我的博客</h1>
        
        <div class="post">
            <h3>Spring Boot 入门指南</h3>
            <p class="post-date">2024年1月15日</p>
            <p>这是一篇关于 Spring Boot 框架的入门指南。Spring Boot 是一个基于 Spring 框架的快速开发工具...</p>
            <a href="/posts/1" class="continue-link">继续阅读</a>
        </div>
        
        <div class="post">
            <h3>React 组件最佳实践</h3>
            <p class="post-date">2024年1月16日</p>
            <p>在这篇文章中，我们将探讨 React 组件开发的最佳实践，包括组件设计原则、状态管理等...</p>
            <a href="/posts/2" class="continue-link">继续阅读</a>
        </div>
        
        <div class="post">
            <h3>数据库设计原则</h3>
            <p class="post-date">2024年1月17日</p>
            <p>良好的数据库设计是应用程序成功的关键。本文将介绍数据库设计的基本原则和最佳实践...</p>
            <a href="/posts/3" class="continue-link">继续阅读</a>
        </div>
        
        <div class="create-form">
            <h2>创建新文章</h2>
            <form>
                <div style="margin-bottom: 15px;">
                    <label>标题:</label><br>
                    <input type="text" name="title" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                </div>
                <div style="margin-bottom: 15px;">
                    <label>内容:</label><br>
                    <textarea name="content" rows="5" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;"></textarea>
                </div>
                <div>
                    <button type="submit" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 3px;">发布文章</button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>`;

    console.log(chalk.green('✅ 测试页面内容已准备'));
  }

  /**
   * 提取 JSP 页面内容
   */
  async extractJSPContent() {
    console.log(chalk.blue('\n📋 步骤 3: 提取 JSP 页面内容'));

    const page = await this.browser.newPage();
    
    try {
      await page.setContent(this.jspPageContent);
      await page.waitForTimeout(1000); // 等待页面渲染

      const contentStructure = await this.contentComparator.extractPageContent(page);

      // 截图
      const screenshotDir = './comprehensive-validation-results';
      await fs.ensureDir(screenshotDir);
      await page.screenshot({ 
        path: path.join(screenshotDir, 'jsp-page.png'),
        fullPage: true 
      });

      console.log(chalk.green('✅ JSP 页面内容提取完成'));
      console.log(chalk.gray(`  标题: "${contentStructure.title}"`));
      console.log(chalk.gray(`  标题数量: ${contentStructure.structure.headings.length}`));
      console.log(chalk.gray(`  段落数量: ${contentStructure.structure.paragraphs.length}`));
      console.log(chalk.gray(`  表单数量: ${contentStructure.structure.forms.length}`));
      console.log(chalk.gray(`  链接数量: ${contentStructure.structure.links.length}`));

      return contentStructure;

    } finally {
      await page.close();
    }
  }

  /**
   * 提取 React 页面内容
   */
  async extractReactContent() {
    console.log(chalk.blue('\n📋 步骤 4: 提取 React 页面内容'));

    const page = await this.browser.newPage();
    
    try {
      await page.setContent(this.reactPageContent);
      await page.waitForTimeout(1000); // 等待页面渲染

      const contentStructure = await this.contentComparator.extractPageContent(page);

      // 截图
      const screenshotDir = './comprehensive-validation-results';
      await page.screenshot({ 
        path: path.join(screenshotDir, 'react-page.png'),
        fullPage: true 
      });

      console.log(chalk.green('✅ React 页面内容提取完成'));
      console.log(chalk.gray(`  标题: "${contentStructure.title}"`));
      console.log(chalk.gray(`  标题数量: ${contentStructure.structure.headings.length}`));
      console.log(chalk.gray(`  段落数量: ${contentStructure.structure.paragraphs.length}`));
      console.log(chalk.gray(`  表单数量: ${contentStructure.structure.forms.length}`));
      console.log(chalk.gray(`  链接数量: ${contentStructure.structure.links.length}`));

      return contentStructure;

    } finally {
      await page.close();
    }
  }

  /**
   * 对比内容
   */
  async compareContents(jspContent, reactContent) {
    console.log(chalk.blue('\n📋 步骤 5: 智能内容对比'));

    const comparison = await this.contentComparator.comparePageContents(jspContent, reactContent);

    console.log(chalk.green('✅ 智能对比完成'));
    console.log(chalk.cyan(`🔍 详细分析结果:`));
    console.log(chalk.gray(`  结构相似度: ${(comparison.structureScore * 100).toFixed(1)}%`));
    console.log(chalk.gray(`  内容相似度: ${(comparison.contentScore * 100).toFixed(1)}%`));
    console.log(chalk.gray(`  语义相似度: ${(comparison.semanticScore * 100).toFixed(1)}%`));
    console.log(chalk.green(`  总体相似度: ${(comparison.overallScore * 100).toFixed(1)}%`));

    if (comparison.recommendations.length > 0) {
      console.log(chalk.blue('💡 改进建议:'));
      comparison.recommendations.forEach(rec => {
        console.log(chalk.gray(`  - ${rec}`));
      });
    }

    return comparison;
  }

  /**
   * 生成详细报告
   */
  async generateDetailedReport(jspContent, reactContent, comparison) {
    console.log(chalk.blue('\n📋 步骤 6: 生成详细报告'));

    const reportDir = './comprehensive-validation-results';
    const reportPath = path.join(reportDir, 'comprehensive-report.html');

    const htmlContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSP2React 综合验证报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 30px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .section { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .score-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .score-card { text-align: center; padding: 20px; border-radius: 8px; }
        .score-excellent { background: #d4edda; color: #155724; }
        .score-good { background: #fff3cd; color: #856404; }
        .score-fair { background: #f8d7da; color: #721c24; }
        .score-number { font-size: 2em; font-weight: bold; }
        .score-label { font-size: 0.9em; margin-top: 5px; }
        .comparison-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .comparison-table th, .comparison-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .comparison-table th { background: #f8f9fa; }
        .screenshots { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .screenshot-container { text-align: center; }
        .screenshot { max-width: 100%; border: 1px solid #ddd; border-radius: 5px; }
        .recommendations { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔬 JSP2React 综合验证报告</h1>
            <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
            <p>本报告展示了 JSP 到 React 转换的智能验证结果</p>
        </div>

        <div class="section">
            <h2>📊 相似度分析</h2>
            <div class="score-grid">
                <div class="score-card ${this.getScoreClass(comparison.structureScore)}">
                    <div class="score-number">${(comparison.structureScore * 100).toFixed(1)}%</div>
                    <div class="score-label">结构相似度</div>
                </div>
                <div class="score-card ${this.getScoreClass(comparison.contentScore)}">
                    <div class="score-number">${(comparison.contentScore * 100).toFixed(1)}%</div>
                    <div class="score-label">内容相似度</div>
                </div>
                <div class="score-card ${this.getScoreClass(comparison.semanticScore)}">
                    <div class="score-number">${(comparison.semanticScore * 100).toFixed(1)}%</div>
                    <div class="score-label">语义相似度</div>
                </div>
                <div class="score-card ${this.getScoreClass(comparison.overallScore)}">
                    <div class="score-number">${(comparison.overallScore * 100).toFixed(1)}%</div>
                    <div class="score-label">总体相似度</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📋 详细对比</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>项目</th>
                        <th>JSP 页面</th>
                        <th>React 页面</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>页面标题</td>
                        <td>${jspContent.title}</td>
                        <td>${reactContent.title}</td>
                        <td>${jspContent.title === reactContent.title ? '✅ 一致' : '⚠️ 不同'}</td>
                    </tr>
                    <tr>
                        <td>标题数量</td>
                        <td>${jspContent.structure.headings.length}</td>
                        <td>${reactContent.structure.headings.length}</td>
                        <td>${jspContent.structure.headings.length === reactContent.structure.headings.length ? '✅ 一致' : '⚠️ 不同'}</td>
                    </tr>
                    <tr>
                        <td>段落数量</td>
                        <td>${jspContent.structure.paragraphs.length}</td>
                        <td>${reactContent.structure.paragraphs.length}</td>
                        <td>${jspContent.structure.paragraphs.length === reactContent.structure.paragraphs.length ? '✅ 一致' : '⚠️ 不同'}</td>
                    </tr>
                    <tr>
                        <td>表单数量</td>
                        <td>${jspContent.structure.forms.length}</td>
                        <td>${reactContent.structure.forms.length}</td>
                        <td>${jspContent.structure.forms.length === reactContent.structure.forms.length ? '✅ 一致' : '⚠️ 不同'}</td>
                    </tr>
                    <tr>
                        <td>链接数量</td>
                        <td>${jspContent.structure.links.length}</td>
                        <td>${reactContent.structure.links.length}</td>
                        <td>${jspContent.structure.links.length === reactContent.structure.links.length ? '✅ 一致' : '⚠️ 不同'}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>📸 页面截图对比</h2>
            <div class="screenshots">
                <div class="screenshot-container">
                    <h3>JSP 页面</h3>
                    <img src="jsp-page.png" alt="JSP 页面截图" class="screenshot">
                </div>
                <div class="screenshot-container">
                    <h3>React 页面</h3>
                    <img src="react-page.png" alt="React 页面截图" class="screenshot">
                </div>
            </div>
        </div>

        ${comparison.recommendations.length > 0 ? `
        <div class="section">
            <h2>💡 改进建议</h2>
            <div class="recommendations">
                <ul>
                    ${comparison.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                </ul>
            </div>
        </div>
        ` : ''}

        <div class="section">
            <h2>🔧 技术说明</h2>
            <p>本验证报告使用了以下技术:</p>
            <ul>
                <li><strong>智能内容提取</strong>: 使用 Puppeteer 提取页面的 DOM 结构和内容</li>
                <li><strong>多维度对比</strong>: 从结构、内容、语义三个维度进行相似度分析</li>
                <li><strong>自动截图</strong>: 生成页面截图用于视觉对比</li>
                <li><strong>智能建议</strong>: 基于分析结果提供改进建议</li>
            </ul>
        </div>
    </div>
</body>
</html>`;

    await fs.writeFile(reportPath, htmlContent);
    console.log(chalk.green(`✅ 详细报告已生成: ${reportPath}`));
    console.log(chalk.cyan(`🌐 在浏览器中查看: file://${path.resolve(reportPath)}`));
  }

  /**
   * 获取分数样式类
   */
  getScoreClass(score) {
    if (score >= 0.8) return 'score-excellent';
    if (score >= 0.6) return 'score-good';
    return 'score-fair';
  }

  /**
   * 清理资源
   */
  async cleanup() {
    console.log(chalk.blue('\n🧹 清理资源...'));
    
    try {
      if (this.browser) {
        await this.browser.close();
        console.log(chalk.gray('✓ Puppeteer 浏览器已关闭'));
      }
      console.log(chalk.green('✅ 资源清理完成'));
    } catch (error) {
      console.log(chalk.yellow(`⚠️  清理过程中出现错误: ${error.message}`));
    }
  }
}

// 运行测试
if (require.main === module) {
  const test = new ComprehensiveValidationTest();
  test.run().catch(error => {
    console.error(chalk.red('测试运行失败:'), error);
    process.exit(1);
  });
}

module.exports = { ComprehensiveValidationTest };
