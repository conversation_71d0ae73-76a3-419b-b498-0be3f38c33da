#!/usr/bin/env node

/**
 * 简单的验证测试脚本
 * 测试核心功能而不依赖复杂的浏览器环境
 */

const puppeteer = require('puppeteer');
const { ContentComparator } = require('./src/tools/ContentComparator');
const chalk = require('chalk');
const fs = require('fs-extra');
const path = require('path');

class SimpleValidationTest {
  constructor() {
    this.browser = null;
    this.contentComparator = new ContentComparator({ verbose: true });
  }

  async run() {
    console.log(chalk.blue('🧪 简单验证测试'));
    console.log(chalk.gray('测试核心验证功能'));
    console.log(chalk.gray('=' .repeat(50)));

    try {
      // 1. 测试内容对比功能
      await this.testContentComparison();

      // 2. 测试 Puppeteer 基础功能
      await this.testPuppeteerBasics();

      // 3. 如果 React 服务器运行，测试实际页面
      await this.testRealPage();

      console.log(chalk.green('\n🎉 简单验证测试完成！'));

    } catch (error) {
      console.error(chalk.red('\n❌ 测试失败:'), error.message);
      console.error(chalk.gray(error.stack));
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 测试内容对比功能
   */
  async testContentComparison() {
    console.log(chalk.blue('\n📋 测试 1: 内容对比功能'));

    // 创建模拟的 JSP 和 React 页面内容
    const jspContent = {
      title: 'JSP 博客页面',
      textContent: '欢迎来到我们的博客 这是第一篇文章 这是第二篇文章 创建新文章',
      structure: {
        headings: [
          { level: 1, text: '欢迎来到我们的博客', id: 'welcome' },
          { level: 3, text: '第一篇文章', id: 'post1' },
          { level: 3, text: '第二篇文章', id: 'post2' }
        ],
        paragraphs: [
          { text: '这是第一篇文章的内容。', length: 10 },
          { text: '这是第二篇文章的内容。', length: 10 }
        ],
        forms: [
          { action: '/posts', method: 'get', inputCount: 1 }
        ],
        links: [
          { text: 'Continue', href: '/posts/1' },
          { text: 'Continue', href: '/posts/2' }
        ]
      },
      semanticBlocks: [
        { tag: 'main', text: '主要内容区域包含博客文章列表', wordCount: 8 }
      ]
    };

    const reactContent = {
      title: 'React 博客页面',
      textContent: '欢迎来到我们的博客 这是第一篇文章 这是第二篇文章 创建新文章',
      structure: {
        headings: [
          { level: 1, text: '欢迎来到我们的博客', id: 'welcome' },
          { level: 3, text: '第一篇文章', id: 'post1' },
          { level: 3, text: '第二篇文章', id: 'post2' }
        ],
        paragraphs: [
          { text: '这是第一篇文章的内容。', length: 10 },
          { text: '这是第二篇文章的内容。', length: 10 }
        ],
        forms: [
          { action: '/posts', method: 'get', inputCount: 1 }
        ],
        links: [
          { text: 'Continue', href: '/posts/1' },
          { text: 'Continue', href: '/posts/2' }
        ]
      },
      semanticBlocks: [
        { tag: 'main', text: '主要内容区域包含博客文章列表', wordCount: 8 }
      ]
    };

    console.log(chalk.gray('进行内容对比分析...'));
    const comparison = await this.contentComparator.comparePageContents(jspContent, reactContent);

    console.log(chalk.green('✅ 内容对比完成'));
    console.log(chalk.gray(`  结构相似度: ${(comparison.structureScore * 100).toFixed(1)}%`));
    console.log(chalk.gray(`  内容相似度: ${(comparison.contentScore * 100).toFixed(1)}%`));
    console.log(chalk.gray(`  语义相似度: ${(comparison.semanticScore * 100).toFixed(1)}%`));
    console.log(chalk.gray(`  总体相似度: ${(comparison.overallScore * 100).toFixed(1)}%`));

    if (comparison.recommendations.length > 0) {
      console.log(chalk.blue('💡 改进建议:'));
      comparison.recommendations.forEach(rec => {
        console.log(chalk.gray(`  - ${rec}`));
      });
    }

    return comparison;
  }

  /**
   * 测试 Puppeteer 基础功能
   */
  async testPuppeteerBasics() {
    console.log(chalk.blue('\n📋 测试 2: Puppeteer 基础功能'));

    try {
      console.log(chalk.gray('启动 Puppeteer 浏览器...'));
      this.browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });

      const page = await this.browser.newPage();
      
      // 测试访问一个简单的页面
      console.log(chalk.gray('访问测试页面...'));
      await page.setContent(`
        <html>
          <head><title>测试页面</title></head>
          <body>
            <h1>这是一个测试页面</h1>
            <p>用于测试 Puppeteer 功能</p>
            <form>
              <input type="text" placeholder="测试输入">
              <button type="submit">提交</button>
            </form>
          </body>
        </html>
      `);

      // 提取页面内容
      console.log(chalk.gray('提取页面内容...'));
      const contentStructure = await this.contentComparator.extractPageContent(page);

      console.log(chalk.green('✅ Puppeteer 基础功能测试完成'));
      console.log(chalk.gray(`  页面标题: "${contentStructure.title}"`));
      console.log(chalk.gray(`  标题数量: ${contentStructure.structure.headings.length}`));
      console.log(chalk.gray(`  段落数量: ${contentStructure.structure.paragraphs.length}`));
      console.log(chalk.gray(`  表单数量: ${contentStructure.structure.forms.length}`));

      // 截图测试
      const screenshotDir = './simple-validation-results';
      await fs.ensureDir(screenshotDir);
      const screenshotPath = path.join(screenshotDir, 'test-page.png');
      await page.screenshot({ path: screenshotPath });
      console.log(chalk.green(`✅ 截图已保存: ${screenshotPath}`));

      await page.close();
      return contentStructure;

    } catch (error) {
      console.log(chalk.yellow(`⚠️  Puppeteer 测试失败: ${error.message}`));
      return null;
    }
  }

  /**
   * 测试实际页面（如果可用）
   */
  async testRealPage() {
    console.log(chalk.blue('\n📋 测试 3: 实际页面测试'));

    if (!this.browser) {
      console.log(chalk.yellow('⚠️  跳过实际页面测试（Puppeteer 不可用）'));
      return;
    }

    try {
      // 检查 React 服务器是否运行
      const http = require('http');
      const serverRunning = await new Promise((resolve) => {
        const req = http.get('http://localhost:3000', (res) => {
          resolve(res.statusCode === 200);
        });
        req.on('error', () => resolve(false));
        req.setTimeout(2000, () => {
          req.destroy();
          resolve(false);
        });
      });

      if (!serverRunning) {
        console.log(chalk.yellow('⚠️  React 服务器未运行，跳过实际页面测试'));
        return;
      }

      console.log(chalk.gray('访问 React 应用首页...'));
      const page = await this.browser.newPage();
      
      try {
        await page.goto('http://localhost:3000', { 
          waitUntil: 'networkidle0',
          timeout: 10000 
        });

        // 提取页面内容
        const contentStructure = await this.contentComparator.extractPageContent(page);

        console.log(chalk.green('✅ 实际页面测试完成'));
        console.log(chalk.gray(`  页面标题: "${contentStructure.title}"`));
        console.log(chalk.gray(`  标题数量: ${contentStructure.structure.headings.length}`));
        console.log(chalk.gray(`  段落数量: ${contentStructure.structure.paragraphs.length}`));
        console.log(chalk.gray(`  链接数量: ${contentStructure.structure.links.length}`));

        // 截图
        const screenshotDir = './simple-validation-results';
        const screenshotPath = path.join(screenshotDir, 'react-homepage.png');
        await page.screenshot({ path: screenshotPath, fullPage: true });
        console.log(chalk.green(`✅ React 首页截图已保存: ${screenshotPath}`));

        return contentStructure;

      } finally {
        await page.close();
      }

    } catch (error) {
      console.log(chalk.yellow(`⚠️  实际页面测试失败: ${error.message}`));
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    console.log(chalk.blue('\n🧹 清理资源...'));
    
    try {
      if (this.browser) {
        await this.browser.close();
        console.log(chalk.gray('✓ Puppeteer 浏览器已关闭'));
      }
      console.log(chalk.green('✅ 资源清理完成'));
    } catch (error) {
      console.log(chalk.yellow(`⚠️  清理过程中出现错误: ${error.message}`));
    }
  }
}

// 运行测试
if (require.main === module) {
  const test = new SimpleValidationTest();
  test.run().catch(error => {
    console.error(chalk.red('测试运行失败:'), error);
    process.exit(1);
  });
}

module.exports = { SimpleValidationTest };
