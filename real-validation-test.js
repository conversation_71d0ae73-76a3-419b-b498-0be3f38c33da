#!/usr/bin/env node

/**
 * 真实的验证测试脚本
 * 测试实际运行的 React 应用
 */

const { PuppeteerValidator } = require('./src/tools/PuppeteerValidator');
const { ContentComparator } = require('./src/tools/ContentComparator');
const chalk = require('chalk');
const fs = require('fs-extra');
const path = require('path');

class RealValidationTest {
  constructor() {
    this.validator = null;
    this.contentComparator = new ContentComparator({ verbose: true });
  }

  async run() {
    console.log(chalk.blue('🧪 真实应用验证测试'));
    console.log(chalk.gray('测试实际运行的 React 应用'));
    console.log(chalk.gray('=' .repeat(60)));

    try {
      // 1. 检查服务器状态
      await this.checkServers();

      // 2. 创建验证器
      await this.createValidator();

      // 3. 测试单个页面
      await this.testSinglePage();

      // 4. 测试内容提取和对比
      await this.testContentExtraction();

      // 5. 生成报告
      await this.generateReport();

      console.log(chalk.green('\n🎉 真实验证测试完成！'));

    } catch (error) {
      console.error(chalk.red('\n❌ 测试失败:'), error.message);
      console.error(chalk.gray(error.stack));
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 检查服务器状态
   */
  async checkServers() {
    console.log(chalk.blue('\n📋 步骤 1: 检查服务器状态'));

    // 检查 React 服务器
    try {
      // 使用 Node.js 内置的 http 模块而不是 fetch
      const http = require('http');

      await new Promise((resolve, reject) => {
        const req = http.get('http://localhost:3000', (res) => {
          if (res.statusCode === 200) {
            console.log(chalk.green('✅ React 服务器运行正常 (http://localhost:3000)'));
            resolve();
          } else {
            reject(new Error(`React 服务器响应异常: ${res.statusCode}`));
          }
        });

        req.on('error', (error) => {
          reject(error);
        });

        req.setTimeout(5000, () => {
          req.destroy();
          reject(new Error('请求超时'));
        });
      });
    } catch (error) {
      console.log(chalk.red('❌ React 服务器无法访问:'), error.message);
      console.log(chalk.yellow('请确保 React 服务器已启动: cd fixtures/target && npm run dev'));
      throw error;
    }

    // JSP 服务器暂时跳过（需要 Maven 和 Tomcat）
    console.log(chalk.yellow('⚠️  JSP 服务器暂时跳过（需要 Maven 环境）'));
  }

  /**
   * 创建验证器
   */
  async createValidator() {
    console.log(chalk.blue('\n📋 步骤 2: 创建验证器'));

    this.validator = new PuppeteerValidator({
      jspBaseUrl: 'http://localhost:8080',
      reactBaseUrl: 'http://localhost:3000',
      headless: false, // 显示浏览器窗口以便观察
      screenshotDir: './real-validation-results',
      verbose: true,
      performanceMonitoring: true,
      visualComparison: true,
      errorAnalysis: true
    });

    await this.validator.initialize();
    console.log(chalk.green('✅ 验证器创建成功'));
  }

  /**
   * 测试单个页面
   */
  async testSinglePage() {
    console.log(chalk.blue('\n📋 步骤 3: 测试 React 页面'));

    // 创建一个简单的测试页面
    const testPageContent = `
import React from 'react';
import Posts from '../components/Posts';

export default function TestPage() {
  const samplePosts = [
    {
      postId: 1,
      title: '第一篇博客文章',
      postedText: '这是第一篇博客文章的内容。它包含了一些示例文本来测试我们的验证功能。',
      postedDate: '2024-01-15'
    },
    {
      postId: 2,
      title: '第二篇博客文章',
      postedText: '这是第二篇博客文章。我们用它来测试列表渲染和数据显示功能。',
      postedDate: '2024-01-16'
    }
  ];

  return (
    <div>
      <h1>博客测试页面</h1>
      <Posts posts={samplePosts} />
    </div>
  );
}
    `;

    // 创建测试页面文件
    const testPagePath = './fixtures/target/src/app/test/page.tsx';
    await fs.ensureDir(path.dirname(testPagePath));
    await fs.writeFile(testPagePath, testPageContent);
    console.log(chalk.green('✅ 测试页面已创建: /test'));

    // 等待一下让 Next.js 重新编译
    console.log(chalk.gray('等待 Next.js 重新编译...'));
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 验证 React 页面
    console.log(chalk.gray('开始验证 React 页面...'));
    const reactResult = await this.validator.validateReactPage('http://localhost:3000/test', 'TestPage');
    
    console.log(chalk.green('✅ React 页面验证完成'));
    console.log(chalk.gray(`  可访问性: ${reactResult.accessible ? '✅' : '❌'}`));
    console.log(chalk.gray(`  错误数量: ${reactResult.errors.length}`));
    console.log(chalk.gray(`  警告数量: ${reactResult.warnings.length}`));
    console.log(chalk.gray(`  加载时间: ${reactResult.loadTime}ms`));

    if (reactResult.errors.length > 0) {
      console.log(chalk.yellow('⚠️  发现错误:'));
      reactResult.errors.slice(0, 3).forEach(error => {
        console.log(chalk.gray(`  - ${typeof error === 'string' ? error : error.message}`));
      });
    }

    return reactResult;
  }

  /**
   * 测试内容提取和对比
   */
  async testContentExtraction() {
    console.log(chalk.blue('\n📋 步骤 4: 测试内容提取和对比'));

    // 使用 Puppeteer 提取页面内容
    const browser = await this.validator.browser;
    const page = await browser.newPage();
    
    try {
      console.log(chalk.gray('访问测试页面...'));
      await page.goto('http://localhost:3000/test', { waitUntil: 'networkidle0' });
      
      console.log(chalk.gray('提取页面内容结构...'));
      const contentStructure = await this.contentComparator.extractPageContent(page);
      
      console.log(chalk.green('✅ 内容提取完成'));
      console.log(chalk.gray(`  页面标题: "${contentStructure.title}"`));
      console.log(chalk.gray(`  标题数量: ${contentStructure.structure.headings.length}`));
      console.log(chalk.gray(`  段落数量: ${contentStructure.structure.paragraphs.length}`));
      console.log(chalk.gray(`  表单数量: ${contentStructure.structure.forms.length}`));
      console.log(chalk.gray(`  链接数量: ${contentStructure.structure.links.length}`));
      console.log(chalk.gray(`  文本长度: ${contentStructure.textContent.length} 字符`));

      // 显示提取的标题
      if (contentStructure.structure.headings.length > 0) {
        console.log(chalk.blue('📝 提取的标题:'));
        contentStructure.structure.headings.forEach(heading => {
          console.log(chalk.gray(`  H${heading.level}: "${heading.text}"`));
        });
      }

      // 显示提取的段落（前3个）
      if (contentStructure.structure.paragraphs.length > 0) {
        console.log(chalk.blue('📄 提取的段落（前3个）:'));
        contentStructure.structure.paragraphs.slice(0, 3).forEach((para, index) => {
          const preview = para.text.length > 50 ? para.text.substring(0, 50) + '...' : para.text;
          console.log(chalk.gray(`  ${index + 1}: "${preview}"`));
        });
      }

      // 截图
      const screenshotPath = './real-validation-results/test-page-screenshot.png';
      await fs.ensureDir(path.dirname(screenshotPath));
      await page.screenshot({ path: screenshotPath, fullPage: true });
      console.log(chalk.green(`✅ 截图已保存: ${screenshotPath}`));

      return contentStructure;

    } finally {
      await page.close();
    }
  }

  /**
   * 生成报告
   */
  async generateReport() {
    console.log(chalk.blue('\n📋 步骤 5: 生成验证报告'));

    const reportDir = './real-validation-results';
    await fs.ensureDir(reportDir);

    // 生成简单的 HTML 报告
    const htmlContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实验证测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background: #f0f8ff; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { border-color: #4CAF50; background: #f9fff9; }
        .warning { border-color: #ff9800; background: #fff8f0; }
        .screenshot { max-width: 100%; border: 1px solid #ddd; border-radius: 5px; }
        .code { background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 JSP2React 真实验证测试报告</h1>
        <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
        <p>测试目标: React 应用内容提取和分析</p>
    </div>

    <div class="section success">
        <h2>✅ 测试结果</h2>
        <ul>
            <li>React 服务器连接: 成功</li>
            <li>页面内容提取: 成功</li>
            <li>截图生成: 成功</li>
            <li>结构分析: 成功</li>
        </ul>
    </div>

    <div class="section">
        <h2>📊 页面分析结果</h2>
        <p>测试页面: <a href="http://localhost:3000/test" target="_blank">http://localhost:3000/test</a></p>
        <p>这个测试验证了我们的智能验证系统能够:</p>
        <ul>
            <li>自动提取页面的 DOM 结构</li>
            <li>分析页面内容（标题、段落、表单等）</li>
            <li>生成页面截图</li>
            <li>监控页面性能</li>
        </ul>
    </div>

    <div class="section">
        <h2>📸 页面截图</h2>
        <img src="test-page-screenshot.png" alt="测试页面截图" class="screenshot">
    </div>

    <div class="section warning">
        <h2>⚠️ 注意事项</h2>
        <p>这是一个演示测试，展示了验证系统的核心功能。在实际使用中，系统会:</p>
        <ul>
            <li>同时验证 JSP 和 React 页面</li>
            <li>进行智能内容对比</li>
            <li>使用 AI 进行语义分析</li>
            <li>生成详细的差异报告</li>
        </ul>
    </div>

    <div class="section">
        <h2>🔧 技术实现</h2>
        <p>本次测试使用了以下技术:</p>
        <div class="code">
- Puppeteer: 浏览器自动化和页面分析<br>
- Content Extraction: 智能 DOM 结构提取<br>
- Performance Monitoring: 页面性能监控<br>
- Screenshot Capture: 自动截图功能
        </div>
    </div>
</body>
</html>`;

    const reportPath = path.join(reportDir, 'validation-report.html');
    await fs.writeFile(reportPath, htmlContent);
    console.log(chalk.green(`✅ HTML 报告已生成: ${reportPath}`));
    console.log(chalk.cyan(`🌐 在浏览器中查看: file://${path.resolve(reportPath)}`));
  }

  /**
   * 清理资源
   */
  async cleanup() {
    console.log(chalk.blue('\n🧹 清理资源...'));
    
    try {
      if (this.validator) {
        await this.validator.close();
      }
      
      // 清理测试文件
      const testPagePath = './fixtures/target/src/app/test/page.tsx';
      if (await fs.pathExists(testPagePath)) {
        await fs.remove(path.dirname(testPagePath));
        console.log(chalk.gray('✓ 测试页面已清理'));
      }
      
      console.log(chalk.green('✅ 资源清理完成'));
    } catch (error) {
      console.log(chalk.yellow(`⚠️  清理过程中出现错误: ${error.message}`));
    }
  }
}

// 运行测试
if (require.main === module) {
  const test = new RealValidationTest();
  test.run().catch(error => {
    console.error(chalk.red('测试运行失败:'), error);
    process.exit(1);
  });
}

module.exports = { RealValidationTest };
