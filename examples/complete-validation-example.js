#!/usr/bin/env node

/**
 * 完整的 JSP2React 验证示例
 * 展示所有验证功能的综合使用
 */

const { PuppeteerValidator } = require('../src/tools/PuppeteerValidator');
const { ProcessManager } = require('../src/tools/ProcessManager');
const { ContentComparator } = require('../src/tools/ContentComparator');
const { ValidationConfig } = require('../src/config/validation-config');
const chalk = require('chalk');
const fs = require('fs-extra');
const path = require('path');

class CompleteValidationExample {
  constructor() {
    this.processManager = new ProcessManager({ verbose: true });
    this.runningProcesses = [];
    this.config = null;
    this.validator = null;
  }

  async run() {
    console.log(chalk.blue('🚀 JSP2React 完整验证示例'));
    console.log(chalk.gray('展示智能验证、进程管理和配置管理的完整功能'));
    console.log(chalk.gray('=' .repeat(80)));

    try {
      // 步骤 1: 初始化配置
      await this.initializeConfiguration();

      // 步骤 2: 准备测试环境
      await this.prepareTestEnvironment();

      // 步骤 3: 启动服务器
      await this.startServers();

      // 步骤 4: 运行智能验证
      await this.runIntelligentValidation();

      // 步骤 5: 分析结果
      await this.analyzeResults();

      // 步骤 6: 生成报告
      await this.generateReports();

      console.log(chalk.green('\n🎉 完整验证示例执行成功！'));
      console.log(chalk.blue('📊 查看生成的报告了解详细结果'));

    } catch (error) {
      console.error(chalk.red('\n❌ 验证示例执行失败:'), error.message);
      if (error.stack) {
        console.error(chalk.gray(error.stack));
      }
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 初始化配置
   */
  async initializeConfiguration() {
    console.log(chalk.blue('\n📋 步骤 1: 初始化配置'));

    // 创建自定义配置
    this.config = new ValidationConfig();
    
    // 自定义配置项
    this.config.set('general.verbose', true);
    this.config.set('general.screenshotDir', './examples/validation-results');
    this.config.set('thresholds.structure.excellent', 0.95);
    this.config.set('thresholds.content.excellent', 0.90);
    this.config.set('thresholds.semantic.excellent', 0.85);
    this.config.set('ai.enabled', !!process.env.OPENAI_API_KEY);

    // 验证配置
    const validation = this.config.validate();
    if (validation.valid) {
      console.log(chalk.green('✅ 配置验证通过'));
    } else {
      console.log(chalk.yellow('⚠️  配置警告:'));
      validation.errors.forEach(error => {
        console.log(chalk.gray(`  - ${error}`));
      });
    }

    // 保存配置示例
    await this.config.saveConfig('./examples/validation-config.json');
    console.log(chalk.green('✅ 配置已保存到示例文件'));
  }

  /**
   * 准备测试环境
   */
  async prepareTestEnvironment() {
    console.log(chalk.blue('\n📋 步骤 2: 准备测试环境'));

    // 确保结果目录存在
    const resultsDir = this.config.get('general.screenshotDir');
    await fs.ensureDir(resultsDir);
    console.log(chalk.green(`✅ 结果目录已创建: ${resultsDir}`));

    // 创建模拟转换结果
    const mockResults = [
      {
        file: {
          name: 'home.jsp',
          relativePath: 'pages/home.jsp',
          path: './fixtures/source/pages/home.jsp'
        },
        componentName: 'HomePage',
        reactCode: `
import React from 'react';

export default function HomePage() {
  return (
    <div className="home-page">
      <header>
        <h1>欢迎来到我们的网站</h1>
        <nav>
          <a href="/about">关于我们</a>
          <a href="/products">产品</a>
          <a href="/contact">联系我们</a>
        </nav>
      </header>
      <main>
        <section>
          <h2>我们的服务</h2>
          <p>我们提供最优质的产品和服务。</p>
          <ul>
            <li>专业咨询</li>
            <li>技术支持</li>
            <li>售后服务</li>
          </ul>
        </section>
        <section>
          <h2>联系表单</h2>
          <form>
            <input type="text" placeholder="姓名" required />
            <input type="email" placeholder="邮箱" required />
            <textarea placeholder="留言"></textarea>
            <button type="submit">提交</button>
          </form>
        </section>
      </main>
      <footer>
        <p>&copy; 2024 我们的公司. 保留所有权利.</p>
      </footer>
    </div>
  );
}
        `.trim(),
        embeddedPath: './fixtures/source/embedded/home.jsp'
      },
      {
        file: {
          name: 'products.jsp',
          relativePath: 'pages/products.jsp',
          path: './fixtures/source/pages/products.jsp'
        },
        componentName: 'ProductsPage',
        reactCode: `
import React from 'react';

export default function ProductsPage() {
  const products = [
    { id: 1, name: '产品 A', price: '¥99' },
    { id: 2, name: '产品 B', price: '¥199' },
    { id: 3, name: '产品 C', price: '¥299' }
  ];

  return (
    <div className="products-page">
      <h1>产品列表</h1>
      <div className="product-grid">
        {products.map(product => (
          <div key={product.id} className="product-card">
            <h3>{product.name}</h3>
            <p className="price">{product.price}</p>
            <button>添加到购物车</button>
          </div>
        ))}
      </div>
    </div>
  );
}
        `.trim(),
        embeddedPath: './fixtures/source/embedded/products.jsp'
      }
    ];

    const resultsPath = path.join(resultsDir, 'conversion-results.json');
    await fs.writeJson(resultsPath, mockResults, { spaces: 2 });
    console.log(chalk.green('✅ 模拟转换结果已创建'));

    return mockResults;
  }

  /**
   * 启动服务器
   */
  async startServers() {
    console.log(chalk.blue('\n📋 步骤 3: 启动服务器'));

    // 检测当前项目类型
    const projectTypes = await this.processManager.detectProjectType('.');
    console.log(chalk.green('✅ 检测到项目类型:'));
    projectTypes.forEach(type => {
      console.log(chalk.gray(`  - ${type.description}`));
    });

    // 模拟服务器状态（实际项目中会启动真实服务器）
    console.log(chalk.yellow('⚠️  演示模式：模拟服务器状态'));
    console.log(chalk.gray('  JSP 服务器: http://localhost:8080 (模拟)'));
    console.log(chalk.gray('  React 服务器: http://localhost:3000 (模拟)'));
  }

  /**
   * 运行智能验证
   */
  async runIntelligentValidation() {
    console.log(chalk.blue('\n📋 步骤 4: 运行智能验证'));

    // 创建验证器
    this.validator = new PuppeteerValidator({
      configPath: './examples/validation-config.json',
      jspBaseUrl: this.config.get('servers.jsp.baseUrl'),
      reactBaseUrl: this.config.get('servers.react.baseUrl'),
      screenshotDir: this.config.get('general.screenshotDir'),
      verbose: this.config.get('general.verbose'),
      performanceMonitoring: true,
      visualComparison: false, // 演示模式关闭视觉对比
      aiEnabled: this.config.get('ai.enabled'),
      aiApiKey: process.env.OPENAI_API_KEY
    });

    console.log(chalk.green('✅ 验证器已创建'));
    console.log(chalk.gray('配置信息:'));
    console.log(chalk.gray(`  AI 分析: ${this.validator.options.aiEnabled ? '启用' : '禁用'}`));
    console.log(chalk.gray(`  性能监控: ${this.validator.options.performanceMonitoring ? '启用' : '禁用'}`));
    console.log(chalk.gray(`  详细日志: ${this.validator.options.verbose ? '启用' : '禁用'}`));

    // 读取转换结果
    const resultsPath = path.join(this.config.get('general.screenshotDir'), 'conversion-results.json');
    const conversionResults = await fs.readJson(resultsPath);

    console.log(chalk.gray(`开始验证 ${conversionResults.length} 个页面...`));
    
    // 在演示模式下，我们模拟验证结果
    console.log(chalk.yellow('⚠️  演示模式：生成模拟验证结果'));
    
    const mockValidationResults = conversionResults.map((result, index) => ({
      fileName: result.file.name,
      componentName: result.componentName,
      success: true,
      results: {
        contentComparison: {
          structureScore: 0.85 + Math.random() * 0.1,
          contentScore: 0.80 + Math.random() * 0.15,
          semanticScore: 0.75 + Math.random() * 0.2,
          overallScore: 0.80 + Math.random() * 0.15,
          recommendations: [
            '转换质量良好，建议优化样式一致性',
            '考虑添加更多的交互功能'
          ]
        }
      }
    }));

    this.validationResults = mockValidationResults;
    console.log(chalk.green(`✅ 验证完成，共验证 ${mockValidationResults.length} 个页面`));
  }

  /**
   * 分析结果
   */
  async analyzeResults() {
    console.log(chalk.blue('\n📋 步骤 5: 分析验证结果'));

    const results = this.validationResults;
    const successCount = results.filter(r => r.success).length;
    const totalScore = results.reduce((sum, r) => sum + (r.results.contentComparison?.overallScore || 0), 0);
    const averageScore = totalScore / results.length;

    console.log(chalk.green('📊 验证统计:'));
    console.log(chalk.gray(`  总页面数: ${results.length}`));
    console.log(chalk.gray(`  成功验证: ${successCount}`));
    console.log(chalk.gray(`  成功率: ${((successCount / results.length) * 100).toFixed(1)}%`));
    console.log(chalk.gray(`  平均相似度: ${(averageScore * 100).toFixed(1)}%`));

    // 分析每个页面的结果
    console.log(chalk.blue('\n📋 详细分析:'));
    results.forEach(result => {
      const score = result.results.contentComparison?.overallScore || 0;
      const scoreColor = score > 0.8 ? chalk.green : score > 0.6 ? chalk.yellow : chalk.red;
      console.log(chalk.gray(`  ${result.fileName}: ${scoreColor((score * 100).toFixed(1) + '%')}`));
    });
  }

  /**
   * 生成报告
   */
  async generateReports() {
    console.log(chalk.blue('\n📋 步骤 6: 生成验证报告'));

    // 模拟报告生成
    const reportDir = this.config.get('general.screenshotDir');
    const htmlReportPath = path.join(reportDir, 'validation-report.html');
    const jsonReportPath = path.join(reportDir, 'validation-report.json');

    // 生成 JSON 报告
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.validationResults.length,
        successful: this.validationResults.filter(r => r.success).length,
        averageScore: this.validationResults.reduce((sum, r) => 
          sum + (r.results.contentComparison?.overallScore || 0), 0) / this.validationResults.length
      },
      results: this.validationResults,
      configuration: this.config.config
    };

    await fs.writeJson(jsonReportPath, report, { spaces: 2 });
    console.log(chalk.green(`✅ JSON 报告已生成: ${jsonReportPath}`));

    // 生成简单的 HTML 报告
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <title>JSP2React 验证报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .summary { margin: 20px 0; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .success { border-color: #4CAF50; }
        .score { font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>JSP2React 验证报告</h1>
        <p>生成时间: ${new Date().toLocaleString()}</p>
    </div>
    <div class="summary">
        <h2>验证摘要</h2>
        <p>总页面数: ${report.summary.total}</p>
        <p>成功验证: ${report.summary.successful}</p>
        <p>平均相似度: ${(report.summary.averageScore * 100).toFixed(1)}%</p>
    </div>
    <div class="results">
        <h2>详细结果</h2>
        ${this.validationResults.map(result => `
            <div class="result success">
                <h3>${result.fileName} → ${result.componentName}</h3>
                <p class="score">相似度: ${((result.results.contentComparison?.overallScore || 0) * 100).toFixed(1)}%</p>
                <ul>
                    ${(result.results.contentComparison?.recommendations || []).map(rec => `<li>${rec}</li>`).join('')}
                </ul>
            </div>
        `).join('')}
    </div>
</body>
</html>`;

    await fs.writeFile(htmlReportPath, htmlContent);
    console.log(chalk.green(`✅ HTML 报告已生成: ${htmlReportPath}`));
    console.log(chalk.cyan(`🌐 在浏览器中打开查看: file://${path.resolve(htmlReportPath)}`));
  }

  /**
   * 清理资源
   */
  async cleanup() {
    console.log(chalk.blue('\n🧹 清理资源...'));
    
    try {
      await this.processManager.stopAllProcesses();
      if (this.validator) {
        await this.validator.close();
      }
      console.log(chalk.green('✅ 资源清理完成'));
    } catch (error) {
      console.log(chalk.yellow(`⚠️  清理过程中出现错误: ${error.message}`));
    }
  }
}

// 运行示例
if (require.main === module) {
  const example = new CompleteValidationExample();
  example.run().catch(error => {
    console.error(chalk.red('示例执行失败:'), error);
    process.exit(1);
  });
}

module.exports = { CompleteValidationExample };
