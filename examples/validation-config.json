{"general": {"timeout": 30000, "headless": true, "verbose": true, "screenshotDir": "./examples/validation-results", "maxConcurrentPages": 3}, "servers": {"jsp": {"baseUrl": "http://localhost:8080", "healthCheckPath": "/", "startupTimeout": 60000, "readyPattern": {}}, "react": {"baseUrl": "http://localhost:3000", "healthCheckPath": "/", "startupTimeout": 60000, "readyPattern": {}}}, "thresholds": {"structure": {"excellent": 0.95, "good": 0.7, "acceptable": 0.5, "poor": 0.3}, "content": {"excellent": 0.9, "good": 0.8, "acceptable": 0.6, "poor": 0.4}, "semantic": {"excellent": 0.85, "good": 0.8, "acceptable": 0.7, "poor": 0.5}, "performance": {"loadTime": {"excellent": 2000, "good": 5000, "acceptable": 10000, "poor": 15000}, "firstContentfulPaint": {"excellent": 1500, "good": 3000, "acceptable": 5000, "poor": 8000}}, "visual": {"excellent": 0.05, "good": 0.15, "acceptable": 0.3, "poor": 0.5}}, "weights": {"structure": 0.3, "content": 0.4, "semantic": 0.2, "performance": 0.1}, "rules": {"required": {"title": true, "headings": true, "forms": true, "navigation": true, "mainContent": true}, "optional": {"images": true, "links": true, "tables": true, "lists": true, "footer": true}, "ignore": {"scripts": true, "styles": true, "comments": true, "whitespace": true, "timestamps": true, "sessionIds": true}, "errorHandling": {"continueOnError": true, "maxErrors": 10, "retryAttempts": 3, "retryDelay": 2000}}, "ai": {"enabled": false, "provider": "openai", "model": "gpt-3.5-turbo", "apiKey": null, "maxTokens": 1000, "temperature": 0.3, "timeout": 30000, "prompts": {"semanticComparison": "\n分析以下两个页面的语义相似度，这是一个 JSP 到 React 的转换验证：\n\nJSP 页面：\n{jsp_content}\n\nReact 页面：\n{react_content}\n\n请返回 JSON 格式的分析结果：\n{\n  \"similarity_score\": 0.85,\n  \"analysis\": \"详细分析\",\n  \"matches\": [\"匹配点\"],\n  \"differences\": [\"差异点\"],\n  \"recommendations\": [\"建议\"]\n}\n          "}}, "reporting": {"formats": ["html", "json"], "includeScreenshots": true, "includePerformance": true, "includeErrors": true, "includeRecommendations": true, "html": {"theme": "default", "showDetails": true, "interactive": true, "embedImages": false}}, "customValidators": [{"name": "formValidation", "enabled": true, "config": {"checkRequired": true, "checkValidation": true, "checkSubmission": true}}, {"name": "navigationValidation", "enabled": true, "config": {"checkMenuItems": true, "checkActiveStates": true, "checkResponsive": true}}]}