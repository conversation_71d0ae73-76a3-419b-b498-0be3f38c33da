#!/usr/bin/env node

/**
 * 智能验证示例
 * 演示如何使用进程管理器和智能内容对比进行 JSP 转 React 验证
 */

const { PuppeteerValidator } = require('../src/tools/PuppeteerValidator');
const { ProcessManager } = require('../src/tools/ProcessManager');
const { ContentComparator } = require('../src/tools/ContentComparator');
const chalk = require('chalk');
const fs = require('fs-extra');
const path = require('path');

class IntelligentValidationDemo {
  constructor() {
    this.processManager = new ProcessManager({ verbose: true });
    this.runningProcesses = [];
  }

  /**
   * 运行完整的智能验证演示
   */
  async run() {
    console.log(chalk.blue('🧠 JSP2React 智能验证演示'));
    console.log(chalk.gray('=' .repeat(60)));

    try {
      // 1. 检查项目环境
      await this.checkEnvironment();

      // 2. 启动服务器
      await this.startServers();

      // 3. 等待服务器就绪
      await this.waitForServers();

      // 4. 运行智能验证
      await this.runIntelligentValidation();

      // 5. 展示结果
      await this.showResults();

      console.log(chalk.green('\n🎉 智能验证演示完成！'));

    } catch (error) {
      console.error(chalk.red('\n❌ 验证演示失败:'), error.message);
      if (error.stack) {
        console.error(chalk.gray(error.stack));
      }
    } finally {
      // 清理资源
      await this.cleanup();
    }
  }

  /**
   * 检查项目环境
   */
  async checkEnvironment() {
    console.log(chalk.blue('\n📋 步骤 1: 检查项目环境'));

    const sourceDir = './fixtures/source';
    const targetDir = './fixtures/target';

    // 检查源项目
    if (!await fs.pathExists(sourceDir)) {
      throw new Error(`源项目目录不存在: ${sourceDir}`);
    }

    const sourceTypes = await this.processManager.detectProjectType(sourceDir);
    console.log(chalk.green(`✅ 检测到源项目类型:`));
    sourceTypes.forEach(type => {
      console.log(chalk.gray(`  - ${type.description} (${type.configFile})`));
    });

    // 检查目标项目
    if (!await fs.pathExists(targetDir)) {
      throw new Error(`目标项目目录不存在: ${targetDir}`);
    }

    const targetTypes = await this.processManager.detectProjectType(targetDir);
    console.log(chalk.green(`✅ 检测到目标项目类型:`));
    targetTypes.forEach(type => {
      console.log(chalk.gray(`  - ${type.description} (${type.configFile})`));
    });

    // 检查转换结果
    const resultsPath = path.join(targetDir, 'conversion-results.json');
    if (!await fs.pathExists(resultsPath)) {
      console.log(chalk.yellow('⚠️  未找到转换结果，创建模拟数据...'));
      await this.createMockConversionResults(resultsPath);
    }
  }

  /**
   * 启动服务器
   */
  async startServers() {
    console.log(chalk.blue('\n📋 步骤 2: 启动服务器'));

    try {
      // 启动 JSP 服务器（如果是 Maven 项目）
      console.log(chalk.gray('启动 JSP 服务器...'));
      const jspServer = await this.processManager.startServer('./fixtures/source', {
        port: 8080,
        preferredType: 'maven'
      });
      this.runningProcesses.push(jspServer);
      console.log(chalk.green(`✅ JSP 服务器已启动: ${jspServer.url}`));

    } catch (error) {
      console.log(chalk.yellow(`⚠️  JSP 服务器启动失败: ${error.message}`));
    }

    try {
      // 启动 React 服务器
      console.log(chalk.gray('启动 React 服务器...'));
      const reactServer = await this.processManager.startServer('./fixtures/target', {
        port: 3000,
        script: 'dev'
      });
      this.runningProcesses.push(reactServer);
      console.log(chalk.green(`✅ React 服务器已启动: ${reactServer.url}`));

    } catch (error) {
      console.log(chalk.yellow(`⚠️  React 服务器启动失败: ${error.message}`));
    }
  }

  /**
   * 等待服务器就绪
   */
  async waitForServers() {
    console.log(chalk.blue('\n📋 步骤 3: 等待服务器就绪'));

    for (const server of this.runningProcesses) {
      console.log(chalk.gray(`等待 ${server.description} 就绪...`));
      const ready = await this.processManager.waitForServer(server.url, 15, 3000);
      
      if (ready) {
        console.log(chalk.green(`✅ ${server.description} 已就绪`));
      } else {
        console.log(chalk.yellow(`⚠️  ${server.description} 可能未完全就绪，继续验证`));
      }
    }
  }

  /**
   * 运行智能验证
   */
  async runIntelligentValidation() {
    console.log(chalk.blue('\n📋 步骤 4: 运行智能验证'));

    // 读取转换结果
    const resultsPath = './fixtures/target/conversion-results.json';
    const conversionResults = await fs.readJson(resultsPath);

    // 创建验证器
    const validator = new PuppeteerValidator({
      jspBaseUrl: 'http://localhost:8080',
      reactBaseUrl: 'http://localhost:3000',
      headless: true,
      screenshotDir: './examples/validation-results',
      verbose: true,
      // 启用智能功能
      performanceMonitoring: true,
      visualComparison: false, // 关闭像素对比，专注内容对比
      errorAnalysis: true,
      structureAnalysis: true,
      // AI 配置（如果有 API Key）
      aiApiKey: process.env.OPENAI_API_KEY,
      aiModel: 'gpt-3.5-turbo'
    });

    console.log(chalk.gray('开始智能验证...'));
    const validationResults = await validator.validateConversion(conversionResults);
    await validator.close();

    console.log(chalk.green(`✅ 验证完成，共验证 ${validationResults.length} 个页面`));
    return validationResults;
  }

  /**
   * 展示结果
   */
  async showResults() {
    console.log(chalk.blue('\n📋 步骤 5: 展示验证结果'));

    const reportPath = './examples/validation-results/validation-report.html';
    if (await fs.pathExists(reportPath)) {
      console.log(chalk.green('📊 验证报告已生成:'));
      console.log(chalk.cyan(`  HTML 报告: ${reportPath}`));
      console.log(chalk.gray('  可以在浏览器中打开查看详细结果'));
    }

    // 显示进程状态
    console.log(chalk.blue('\n🔍 服务器状态:'));
    const processes = this.processManager.listProcesses();
    processes.forEach(process => {
      const status = process.running ? chalk.green('运行中') : chalk.red('已停止');
      console.log(chalk.gray(`  ${process.url}: ${status}`));
    });
  }

  /**
   * 创建模拟转换结果
   */
  async createMockConversionResults(resultsPath) {
    const mockResults = [
      {
        file: {
          name: 'index.jsp',
          relativePath: 'index.jsp',
          path: './fixtures/source/src/main/webapp/index.jsp'
        },
        componentName: 'HomePage',
        reactCode: 'export default function HomePage() { return <div>Home Page</div>; }',
        embeddedPath: './fixtures/source/src/main/webapp/embedded/index.jsp'
      },
      {
        file: {
          name: 'about.jsp',
          relativePath: 'about.jsp',
          path: './fixtures/source/src/main/webapp/about.jsp'
        },
        componentName: 'AboutPage',
        reactCode: 'export default function AboutPage() { return <div>About Page</div>; }',
        embeddedPath: './fixtures/source/src/main/webapp/embedded/about.jsp'
      }
    ];

    await fs.ensureDir(path.dirname(resultsPath));
    await fs.writeJson(resultsPath, mockResults, { spaces: 2 });
    console.log(chalk.gray('  ✓ 创建模拟转换结果'));
  }

  /**
   * 清理资源
   */
  async cleanup() {
    console.log(chalk.blue('\n🧹 清理资源...'));
    
    try {
      await this.processManager.stopAllProcesses();
      console.log(chalk.gray('✓ 所有进程已停止'));
    } catch (error) {
      console.log(chalk.yellow(`⚠️  清理过程中出现错误: ${error.message}`));
    }
  }
}

// 运行演示
if (require.main === module) {
  const demo = new IntelligentValidationDemo();
  demo.run().catch(error => {
    console.error(chalk.red('演示运行失败:'), error);
    process.exit(1);
  });
}

module.exports = { IntelligentValidationDemo };
