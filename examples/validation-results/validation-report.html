
<!DOCTYPE html>
<html>
<head>
    <title>JSP2React 验证报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .summary { margin: 20px 0; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .success { border-color: #4CAF50; }
        .score { font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>JSP2React 验证报告</h1>
        <p>生成时间: 20/07/2025, 1:24:25 pm</p>
    </div>
    <div class="summary">
        <h2>验证摘要</h2>
        <p>总页面数: 2</p>
        <p>成功验证: 2</p>
        <p>平均相似度: 90.8%</p>
    </div>
    <div class="results">
        <h2>详细结果</h2>
        
            <div class="result success">
                <h3>home.jsp → HomePage</h3>
                <p class="score">相似度: 94.0%</p>
                <ul>
                    <li>转换质量良好，建议优化样式一致性</li><li>考虑添加更多的交互功能</li>
                </ul>
            </div>
        
            <div class="result success">
                <h3>products.jsp → ProductsPage</h3>
                <p class="score">相似度: 87.7%</p>
                <ul>
                    <li>转换质量良好，建议优化样式一致性</li><li>考虑添加更多的交互功能</li>
                </ul>
            </div>
        
    </div>
</body>
</html>