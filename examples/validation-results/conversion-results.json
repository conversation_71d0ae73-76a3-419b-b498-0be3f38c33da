[{"file": {"name": "home.jsp", "relativePath": "pages/home.jsp", "path": "./fixtures/source/pages/home.jsp"}, "componentName": "HomePage", "reactCode": "import React from 'react';\n\nexport default function HomePage() {\n  return (\n    <div className=\"home-page\">\n      <header>\n        <h1>欢迎来到我们的网站</h1>\n        <nav>\n          <a href=\"/about\">关于我们</a>\n          <a href=\"/products\">产品</a>\n          <a href=\"/contact\">联系我们</a>\n        </nav>\n      </header>\n      <main>\n        <section>\n          <h2>我们的服务</h2>\n          <p>我们提供最优质的产品和服务。</p>\n          <ul>\n            <li>专业咨询</li>\n            <li>技术支持</li>\n            <li>售后服务</li>\n          </ul>\n        </section>\n        <section>\n          <h2>联系表单</h2>\n          <form>\n            <input type=\"text\" placeholder=\"姓名\" required />\n            <input type=\"email\" placeholder=\"邮箱\" required />\n            <textarea placeholder=\"留言\"></textarea>\n            <button type=\"submit\">提交</button>\n          </form>\n        </section>\n      </main>\n      <footer>\n        <p>&copy; 2024 我们的公司. 保留所有权利.</p>\n      </footer>\n    </div>\n  );\n}", "embeddedPath": "./fixtures/source/embedded/home.jsp"}, {"file": {"name": "products.jsp", "relativePath": "pages/products.jsp", "path": "./fixtures/source/pages/products.jsp"}, "componentName": "ProductsPage", "reactCode": "import React from 'react';\n\nexport default function ProductsPage() {\n  const products = [\n    { id: 1, name: '产品 A', price: '¥99' },\n    { id: 2, name: '产品 B', price: '¥199' },\n    { id: 3, name: '产品 C', price: '¥299' }\n  ];\n\n  return (\n    <div className=\"products-page\">\n      <h1>产品列表</h1>\n      <div className=\"product-grid\">\n        {products.map(product => (\n          <div key={product.id} className=\"product-card\">\n            <h3>{product.name}</h3>\n            <p className=\"price\">{product.price}</p>\n            <button>添加到购物车</button>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}", "embeddedPath": "./fixtures/source/embedded/products.jsp"}]