#!/usr/bin/env node

/**
 * 智能双应用验证脚本
 * 自动配置和启动 JSP 与 React 应用进行对比验证
 */

const { ProcessManager } = require('./src/tools/ProcessManager');
const { PuppeteerValidator } = require('./src/tools/PuppeteerValidator');
const chalk = require('chalk');
const fs = require('fs-extra');
const path = require('path');

class SmartDualValidation {
  constructor() {
    this.processManager = new ProcessManager({ verbose: true });
    this.runningProcesses = [];
    this.validator = null;
    this.cleanupTasks = [];
  }

  async run() {
    console.log(chalk.blue('🤖 智能双应用验证'));
    console.log(chalk.gray('自动配置并启动 JSP 和 React 应用进行智能对比'));
    console.log(chalk.gray('=' .repeat(70)));

    try {
      // 1. 环境检查和项目分析
      await this.analyzeProjects();

      // 2. 智能启动 JSP 应用
      await this.smartStartJSP();

      // 3. 启动 React 应用
      await this.startReact();

      // 4. 等待应用就绪
      await this.waitForReadiness();

      // 5. 执行智能验证
      await this.performIntelligentValidation();

      // 6. 生成智能报告
      await this.generateIntelligentReport();

      console.log(chalk.green('\n🎉 智能验证完成！'));

    } catch (error) {
      console.error(chalk.red('\n❌ 验证失败:'), error.message);
      if (this.options?.verbose) {
        console.error(chalk.gray(error.stack));
      }
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 分析项目结构和配置
   */
  async analyzeProjects() {
    console.log(chalk.blue('\n📋 步骤 1: 项目分析'));

    // 分析 JSP 项目
    const jspPath = './fixtures/source';
    console.log(chalk.gray(`分析 JSP 项目: ${jspPath}`));
    
    if (!await fs.pathExists(jspPath)) {
      throw new Error(`JSP 项目目录不存在: ${jspPath}`);
    }

    const jspTypes = await this.processManager.detectProjectType(jspPath);
    console.log(chalk.green('✅ JSP 项目分析结果:'));
    jspTypes.forEach(type => {
      console.log(chalk.gray(`  - ${type.description} (${type.configFile})`));
    });

    // 检查 JSP 项目的具体配置
    const pomPath = path.join(jspPath, 'pom.xml');
    if (await fs.pathExists(pomPath)) {
      const pomContent = await fs.readFile(pomPath, 'utf8');
      const hasJetty = pomContent.includes('jetty-maven-plugin');
      const hasTomcat = pomContent.includes('tomcat-maven-plugin');
      const hasSpringBoot = pomContent.includes('spring-boot-starter');
      
      console.log(chalk.gray(`  配置检查:`));
      console.log(chalk.gray(`    - Jetty 插件: ${hasJetty ? '✅' : '❌'}`));
      console.log(chalk.gray(`    - Tomcat 插件: ${hasTomcat ? '✅' : '❌'}`));
      console.log(chalk.gray(`    - Spring Boot: ${hasSpringBoot ? '✅' : '❌'}`));
      
      if (!hasJetty && !hasTomcat && !hasSpringBoot) {
        console.log(chalk.yellow('⚠️  项目缺少 Web 服务器配置，将自动添加 Jetty 支持'));
      }
    }

    // 分析 React 项目
    const reactPath = './fixtures/target';
    console.log(chalk.gray(`分析 React 项目: ${reactPath}`));
    
    if (!await fs.pathExists(reactPath)) {
      throw new Error(`React 项目目录不存在: ${reactPath}`);
    }

    const reactTypes = await this.processManager.detectProjectType(reactPath);
    console.log(chalk.green('✅ React 项目分析结果:'));
    reactTypes.forEach(type => {
      console.log(chalk.gray(`  - ${type.description} (${type.configFile})`));
    });

    this.projectInfo = {
      jsp: { path: jspPath, types: jspTypes },
      react: { path: reactPath, types: reactTypes }
    };
  }

  /**
   * 智能启动 JSP 应用
   */
  async smartStartJSP() {
    console.log(chalk.blue('\n📋 步骤 2: 智能启动 JSP 应用'));

    try {
      console.log(chalk.gray('正在智能配置和启动 JSP 应用...'));
      
      const jspServer = await this.processManager.startServer(this.projectInfo.jsp.path, {
        port: 8080,
        preferredType: 'maven'
      });
      
      this.runningProcesses.push(jspServer);
      
      // 如果有清理任务，记录下来
      if (jspServer.cleanup) {
        this.cleanupTasks.push(jspServer.cleanup);
      }
      
      console.log(chalk.green(`✅ JSP 应用启动成功: ${jspServer.url}`));
      console.log(chalk.gray(`  项目类型: ${jspServer.description}`));
      
    } catch (error) {
      console.log(chalk.red(`❌ JSP 应用启动失败: ${error.message}`));
      console.log(chalk.yellow('💡 可能的解决方案:'));
      console.log(chalk.gray('  1. 检查 Java 和 Maven 是否正确安装'));
      console.log(chalk.gray('  2. 确保 8080 端口未被占用'));
      console.log(chalk.gray('  3. 检查项目依赖是否完整'));
      throw error;
    }
  }

  /**
   * 启动 React 应用
   */
  async startReact() {
    console.log(chalk.blue('\n📋 步骤 3: 启动 React 应用'));

    try {
      console.log(chalk.gray('正在启动 React 应用...'));
      
      const reactServer = await this.processManager.startServer(this.projectInfo.react.path, {
        port: 3000,
        script: 'dev'
      });
      
      this.runningProcesses.push(reactServer);
      console.log(chalk.green(`✅ React 应用启动成功: ${reactServer.url}`));
      
    } catch (error) {
      console.log(chalk.red(`❌ React 应用启动失败: ${error.message}`));
      console.log(chalk.yellow('💡 可能的解决方案:'));
      console.log(chalk.gray('  1. 运行 npm install 安装依赖'));
      console.log(chalk.gray('  2. 确保 3000 端口未被占用'));
      console.log(chalk.gray('  3. 检查 Node.js 版本是否兼容'));
      throw error;
    }
  }

  /**
   * 等待应用就绪
   */
  async waitForReadiness() {
    console.log(chalk.blue('\n📋 步骤 4: 等待应用就绪'));

    const servers = [
      { name: 'JSP', url: 'http://localhost:8080', maxAttempts: 30 },
      { name: 'React', url: 'http://localhost:3000', maxAttempts: 20 }
    ];

    for (const server of servers) {
      console.log(chalk.gray(`等待 ${server.name} 应用就绪...`));
      
      const ready = await this.processManager.waitForServer(
        server.url, 
        server.maxAttempts, 
        2000
      );
      
      if (ready) {
        console.log(chalk.green(`✅ ${server.name} 应用已就绪`));
      } else {
        console.log(chalk.yellow(`⚠️  ${server.name} 应用启动超时，但将继续验证`));
      }
    }
  }

  /**
   * 执行智能验证
   */
  async performIntelligentValidation() {
    console.log(chalk.blue('\n📋 步骤 5: 执行智能验证'));

    // 创建智能验证器
    this.validator = new PuppeteerValidator({
      jspBaseUrl: 'http://localhost:8080',
      reactBaseUrl: 'http://localhost:3000',
      headless: true, // 在生产环境中使用 headless 模式
      screenshotDir: './smart-validation-results',
      verbose: true,
      performanceMonitoring: true,
      visualComparison: false, // 专注于内容对比
      errorAnalysis: true,
      structureAnalysis: true
    });

    await this.validator.initialize();
    console.log(chalk.green('✅ 智能验证器已初始化'));

    // 定义要验证的页面
    const pagesToValidate = [
      { jsp: '/posts.jsp', react: '/posts', name: 'Posts' },
      { jsp: '/create.jsp', react: '/create', name: 'Create' },
      { jsp: '/edit.jsp', react: '/edit', name: 'Edit' }
    ];

    this.validationResults = [];

    for (const page of pagesToValidate) {
      console.log(chalk.gray(`验证页面: ${page.name}`));
      
      try {
        // 验证 JSP 页面
        const jspResult = await this.validator.validateJSPPage(
          `http://localhost:8080${page.jsp}`, 
          page.name
        );
        
        // 验证 React 页面
        const reactResult = await this.validator.validateReactPage(
          `http://localhost:3000${page.react}`, 
          page.name
        );
        
        // 进行智能对比
        let comparison = null;
        if (jspResult.accessible && reactResult.accessible) {
          comparison = await this.validator.comparePages(jspResult, reactResult);
        }
        
        this.validationResults.push({
          page: page.name,
          jsp: jspResult,
          react: reactResult,
          comparison: comparison
        });
        
        const status = jspResult.accessible && reactResult.accessible ? '✅' : '⚠️';
        console.log(chalk.gray(`  ${status} ${page.name} 验证完成`));
        
      } catch (error) {
        console.log(chalk.yellow(`  ⚠️  ${page.name} 验证失败: ${error.message}`));
        
        this.validationResults.push({
          page: page.name,
          error: error.message,
          jsp: null,
          react: null,
          comparison: null
        });
      }
    }

    console.log(chalk.green(`✅ 智能验证完成，共验证 ${this.validationResults.length} 个页面`));
  }

  /**
   * 生成智能报告
   */
  async generateIntelligentReport() {
    console.log(chalk.blue('\n📋 步骤 6: 生成智能报告'));

    const reportDir = './smart-validation-results';
    await fs.ensureDir(reportDir);

    // 计算统计信息
    const stats = this.calculateStats();
    
    // 生成 JSON 报告
    const report = {
      timestamp: new Date().toISOString(),
      projectInfo: this.projectInfo,
      statistics: stats,
      results: this.validationResults
    };

    const jsonPath = path.join(reportDir, 'smart-validation-report.json');
    await fs.writeJson(jsonPath, report, { spaces: 2 });
    console.log(chalk.green(`✅ JSON 报告: ${jsonPath}`));

    // 生成 HTML 报告
    const htmlPath = path.join(reportDir, 'smart-validation-report.html');
    const htmlContent = this.generateHTMLReport(report);
    await fs.writeFile(htmlPath, htmlContent);
    console.log(chalk.green(`✅ HTML 报告: ${htmlPath}`));
    console.log(chalk.cyan(`🌐 在浏览器中查看: file://${path.resolve(htmlPath)}`));

    // 显示摘要
    this.displaySummary(stats);
  }

  /**
   * 计算统计信息
   */
  calculateStats() {
    const validResults = this.validationResults.filter(r => !r.error);
    const accessibleJSP = validResults.filter(r => r.jsp?.accessible).length;
    const accessibleReact = validResults.filter(r => r.react?.accessible).length;
    const successfulComparisons = validResults.filter(r => r.comparison).length;
    
    let avgSimilarity = 0;
    if (successfulComparisons > 0) {
      const totalSimilarity = validResults
        .filter(r => r.comparison)
        .reduce((sum, r) => sum + r.comparison.structureSimilarity, 0);
      avgSimilarity = totalSimilarity / successfulComparisons;
    }

    return {
      totalPages: this.validationResults.length,
      validResults: validResults.length,
      accessibleJSP,
      accessibleReact,
      successfulComparisons,
      avgSimilarity,
      errors: this.validationResults.filter(r => r.error).length
    };
  }

  /**
   * 生成 HTML 报告
   */
  generateHTMLReport(report) {
    const stats = report.statistics;
    
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能双应用验证报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 30px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
        .stat-label { color: #666; margin-top: 5px; }
        .section { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .result-item { border: 1px solid #ddd; border-radius: 5px; padding: 15px; margin: 10px 0; }
        .result-success { border-color: #28a745; background: #f8fff9; }
        .result-warning { border-color: #ffc107; background: #fffbf0; }
        .result-error { border-color: #dc3545; background: #fff8f8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 智能双应用验证报告</h1>
            <p>生成时间: ${new Date(report.timestamp).toLocaleString('zh-CN')}</p>
            <p>本报告展示了 JSP 到 React 转换的智能验证结果</p>
        </div>

        <div class="section">
            <h2>📊 验证统计</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">${stats.totalPages}</div>
                    <div class="stat-label">总页面数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.successfulComparisons}</div>
                    <div class="stat-label">成功对比</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.avgSimilarity.toFixed(1)}%</div>
                    <div class="stat-label">平均相似度</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.errors}</div>
                    <div class="stat-label">错误数量</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📋 详细结果</h2>
            ${report.results.map(result => {
              if (result.error) {
                return `<div class="result-item result-error">
                  <h3>${result.page}</h3>
                  <p>❌ 验证失败: ${result.error}</p>
                </div>`;
              }
              
              const jspStatus = result.jsp?.accessible ? '✅' : '❌';
              const reactStatus = result.react?.accessible ? '✅' : '❌';
              const similarity = result.comparison?.structureSimilarity || 0;
              
              const resultClass = similarity >= 80 ? 'result-success' : 
                                 similarity >= 60 ? 'result-warning' : 'result-error';
              
              return `<div class="result-item ${resultClass}">
                <h3>${result.page}</h3>
                <p>JSP: ${jspStatus} | React: ${reactStatus}</p>
                ${result.comparison ? `<p>相似度: ${similarity.toFixed(1)}%</p>` : ''}
              </div>`;
            }).join('')}
        </div>

        <div class="section">
            <h2>🔧 技术说明</h2>
            <p>本验证使用了以下智能技术:</p>
            <ul>
                <li><strong>自动项目配置</strong>: 智能检测和配置不同类型的项目</li>
                <li><strong>动态插件注入</strong>: 为缺少配置的项目自动添加必要的插件</li>
                <li><strong>智能内容对比</strong>: 基于 DOM 结构和语义的深度分析</li>
                <li><strong>多维度验证</strong>: 从可访问性、性能、结构等多个角度验证</li>
            </ul>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * 显示摘要
   */
  displaySummary(stats) {
    console.log(chalk.blue('\n📋 验证摘要:'));
    console.log(chalk.gray(`总页面数: ${stats.totalPages}`));
    console.log(chalk.gray(`成功对比: ${stats.successfulComparisons}`));
    console.log(chalk.gray(`平均相似度: ${stats.avgSimilarity.toFixed(1)}%`));
    
    if (stats.errors > 0) {
      console.log(chalk.red(`错误数量: ${stats.errors}`));
    }
    
    if (stats.avgSimilarity >= 80) {
      console.log(chalk.green('🎉 转换质量优秀！'));
    } else if (stats.avgSimilarity >= 60) {
      console.log(chalk.yellow('⚠️  转换质量良好，有改进空间'));
    } else {
      console.log(chalk.red('❌ 转换质量需要改进'));
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    console.log(chalk.blue('\n🧹 清理资源...'));
    
    try {
      // 关闭验证器
      if (this.validator) {
        await this.validator.close();
        console.log(chalk.gray('✓ 验证器已关闭'));
      }
      
      // 停止所有进程
      await this.processManager.stopAllProcesses();
      console.log(chalk.gray('✓ 所有应用进程已停止'));
      
      // 执行清理任务（删除临时文件等）
      for (const cleanupTask of this.cleanupTasks) {
        try {
          await cleanupTask();
        } catch (error) {
          console.log(chalk.yellow(`⚠️  清理任务失败: ${error.message}`));
        }
      }
      
      if (this.cleanupTasks.length > 0) {
        console.log(chalk.gray('✓ 临时文件已清理'));
      }
      
      console.log(chalk.green('✅ 资源清理完成'));
    } catch (error) {
      console.log(chalk.yellow(`⚠️  清理过程中出现错误: ${error.message}`));
    }
  }
}

// 运行智能验证
if (require.main === module) {
  const validation = new SmartDualValidation();
  validation.run().catch(error => {
    console.error(chalk.red('智能验证失败:'), error);
    process.exit(1);
  });
}

module.exports = { SmartDualValidation };
